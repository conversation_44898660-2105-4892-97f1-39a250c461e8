package postgres

import (
	"context"
	"database/sql"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/pkg/errors"
)

// aiRepository AI仓储实现
type aiRepository struct {
	db *DB
}

// NewAIRepository 创建AI仓储
func NewAIRepository(db *DB) interfaces.AIRepository {
	return &aiRepository{db: db}
}

// GetConfigs 获取所有配置
func (r *aiRepository) GetConfigs(ctx context.Context) ([]interfaces.ConfigItem, error) {
	query := `SELECT id, name, url, actions FROM website_configs ORDER BY id`

	var configs []interfaces.ConfigItem
	err := r.db.conn.SelectContext(ctx, &configs, query)
	if err != nil {
		return nil, errors.NewDatabaseError("GetConfigs", "获取配置列表失败", err)
	}

	return configs, nil
}

// GetConfig 获取指定配置
func (r *aiRepository) GetConfig(ctx context.Context, id string) (*interfaces.ConfigItem, error) {
	query := `SELECT id, name, url, actions FROM website_configs WHERE id = $1`

	var config interfaces.ConfigItem
	err := r.db.conn.GetContext(ctx, &config, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrNotFound
		}
		return nil, errors.NewDatabaseError("GetConfig", "获取配置失败", err)
	}

	return &config, nil
}

// GetPrompts 获取所有提示词
func (r *aiRepository) GetPrompts(ctx context.Context) ([]interfaces.PromptItem, error) {
	query := `SELECT prompt_key, prompt_text, description, category 
			  FROM system_prompts ORDER BY category, prompt_key`

	var prompts []interfaces.PromptItem
	err := r.db.conn.SelectContext(ctx, &prompts, query)
	if err != nil {
		return nil, errors.NewDatabaseError("GetPrompts", "获取提示词列表失败", err)
	}

	return prompts, nil
}

// GetPrompt 获取指定提示词
func (r *aiRepository) GetPrompt(ctx context.Context, key string) (*interfaces.PromptItem, error) {
	query := `SELECT prompt_key, prompt_text, description, category 
			  FROM system_prompts WHERE prompt_key = $1`

	var prompt interfaces.PromptItem
	err := r.db.conn.GetContext(ctx, &prompt, query, key)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrNotFound
		}
		return nil, errors.NewDatabaseError("GetPrompt", "获取提示词失败", err)
	}

	return &prompt, nil
}

// GetSubjects 获取科目列表（只查询 prompt_key 和 description）
func (r *aiRepository) GetSubjects(ctx context.Context) ([]interfaces.SubjectItem, error) {
	query := `SELECT prompt_key, description 
			  FROM system_prompts 
				WHERE category = 'subject'
			  ORDER BY prompt_key`

	var subjects []interfaces.SubjectItem
	err := r.db.conn.SelectContext(ctx, &subjects, query)
	if err != nil {
		return nil, errors.NewDatabaseError("GetSubjects", "获取科目列表失败", err)
	}

	return subjects, nil
}
