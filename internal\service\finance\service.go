package finance

import (
	"context"
	"serverless-aig/internal/domain/finance"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/internal/repository/postgres"
	"serverless-aig/pkg/errors"
	"time"

	"github.com/google/uuid"
)

// Service 财务服务接口
type Service interface {
	AddExpense(ctx context.Context, adminID uuid.UUID, req *finance.AddExpenseRequest) error
	GetExpenses(ctx context.Context, req *finance.ExpenseListRequest) (*finance.ExpenseListResponse, error)
	GetFinancialReport(ctx context.Context, req *finance.FinancialReportRequest) (*finance.FinancialReport, error)
	GetExpenseAnalysis(ctx context.Context, startDate, endDate string) (*finance.ExpenseAnalysis, error)
}

// service 财务服务实现
type service struct {
	financeRepo interfaces.FinanceRepository
	logger      *logger.Logger
}

// NewService 创建财务服务
func NewService(db *postgres.DB, logger *logger.Logger) Service {
	return &service{
		financeRepo: postgres.NewFinanceRepository(db),
		logger:      logger,
	}
}

// AddExpense 添加支出记录
func (s *service) AddExpense(ctx context.Context, adminID uuid.UUID, req *finance.AddExpenseRequest) error {
	// 验证请求参数
	if req.Description == "" {
		return errors.NewValidationError("description", "支出描述不能为空")
	}

	if req.Amount <= 0 {
		return errors.NewValidationError("amount", "支出金额必须大于0")
	}

	if req.Category == "" {
		return errors.NewValidationError("category", "支出类别不能为空")
	}

	if req.Vendor == "" {
		return errors.NewValidationError("vendor", "供应商不能为空")
	}

	// 验证日期格式
	transactionDate, err := time.Parse("2006-01-02", req.TransactionDate)
	if err != nil {
		return errors.NewValidationError("transaction_date", "交易日期格式错误")
	}

	servicePeriodStart, err := time.Parse("2006-01-02", req.ServicePeriodStart)
	if err != nil {
		return errors.NewValidationError("service_period_start", "服务周期开始日期格式错误")
	}

	servicePeriodEnd, err := time.Parse("2006-01-02", req.ServicePeriodEnd)
	if err != nil {
		return errors.NewValidationError("service_period_end", "服务周期结束日期格式错误")
	}

	// 验证服务周期
	if servicePeriodEnd.Before(servicePeriodStart) {
		return errors.NewValidationError("service_period", "服务周期结束日期不能早于开始日期")
	}

	// 验证支出类别
	category := finance.ExpenseCategory(req.Category)
	if !isValidCategory(category) {
		return errors.NewValidationError("category", "无效的支出类别")
	}

	// 创建支出记录
	expense := &finance.Expense{
		Description:        req.Description,
		Amount:             req.Amount,
		Currency:           "CNY",
		Category:           category,
		Vendor:             req.Vendor,
		TransactionDate:    transactionDate,
		ServicePeriodStart: servicePeriodStart,
		ServicePeriodEnd:   servicePeriodEnd,
		PaymentMethod:      req.PaymentMethod,
		InvoiceURL:         req.InvoiceURL,
		RecordedBy:         &adminID,
		CreatedAt:          time.Now(),
	}

	err = s.financeRepo.CreateExpense(ctx, expense)
	if err != nil {
		s.logger.LogError("创建支出记录失败: %v", err)
		return err
	}

	s.logger.LogInfo("管理员 %s 添加支出记录: %s，金额: %d分，供应商: %s",
		adminID, req.Description, req.Amount, req.Vendor)

	return nil
}

// GetExpenses 获取支出列表
func (s *service) GetExpenses(ctx context.Context, req *finance.ExpenseListRequest) (*finance.ExpenseListResponse, error) {
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 100 {
		req.Limit = 100
	}

	// 验证日期格式
	if req.StartDate != "" {
		if _, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			return nil, errors.NewValidationError("start_date", "开始日期格式错误")
		}
	}

	if req.EndDate != "" {
		if _, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			return nil, errors.NewValidationError("end_date", "结束日期格式错误")
		}
	}

	// 验证类别
	if req.Category != "" {
		category := finance.ExpenseCategory(req.Category)
		if !isValidCategory(category) {
			return nil, errors.NewValidationError("category", "无效的支出类别")
		}
	}

	expenses, total, err := s.financeRepo.GetExpenses(ctx, req)
	if err != nil {
		s.logger.LogError("获取支出列表失败: %v", err)
		return nil, err
	}

	return &finance.ExpenseListResponse{
		Expenses: expenses,
		Count:    len(expenses),
		Total:    total,
	}, nil
}

// GetFinancialReport 获取财务报表
func (s *service) GetFinancialReport(ctx context.Context, req *finance.FinancialReportRequest) (*finance.FinancialReport, error) {
	// 验证日期格式
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, errors.NewValidationError("start_date", "开始日期格式错误")
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return nil, errors.NewValidationError("end_date", "结束日期格式错误")
	}

	// 验证日期范围
	if endDate.Before(startDate) {
		return nil, errors.NewValidationError("date_range", "结束日期不能早于开始日期")
	}

	report, err := s.financeRepo.GetFinancialReport(ctx, req.StartDate, req.EndDate)
	if err != nil {
		s.logger.LogError("获取财务报表失败: %v", err)
		return nil, err
	}

	return report, nil
}

// GetExpenseAnalysis 获取支出分析
func (s *service) GetExpenseAnalysis(ctx context.Context, startDate, endDate string) (*finance.ExpenseAnalysis, error) {
	// 验证日期格式
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, errors.NewValidationError("start_date", "开始日期格式错误")
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, errors.NewValidationError("end_date", "结束日期格式错误")
	}

	// 验证日期范围
	if end.Before(start) {
		return nil, errors.NewValidationError("date_range", "结束日期不能早于开始日期")
	}

	analysis, err := s.financeRepo.GetExpenseAnalysis(ctx, startDate, endDate)
	if err != nil {
		s.logger.LogError("获取支出分析失败: %v", err)
		return nil, err
	}

	return analysis, nil
}

// isValidCategory 验证支出类别是否有效
func isValidCategory(category finance.ExpenseCategory) bool {
	switch category {
	case finance.CategoryInfrastructure, finance.CategorySoftware,
		finance.CategoryMarketing, finance.CategoryOperational, finance.CategoryOther:
		return true
	default:
		return false
	}
}
