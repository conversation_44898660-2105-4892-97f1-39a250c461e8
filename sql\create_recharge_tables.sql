-- 创建充值申请表
CREATE TABLE IF NOT EXISTS recharge_requests (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,  -- 对应于Supabase中的auth.users.id
  amount INTEGER NOT NULL,  -- 充值金额（人民币）
  order_id TEXT NOT NULL UNIQUE,  -- 支付宝订单号，唯一键防止重复充值
  status TEXT NOT NULL DEFAULT 'pending',  -- 充值状态: pending, approved, rejected
  payment_method TEXT NOT NULL DEFAULT 'alipay', -- 支付方式：默认支付宝
  payment_proof TEXT, -- 支付凭证URL或路径
  admin_note TEXT, -- 管理员处理备注
  processed_by TEXT, -- 处理此申请的管理员ID
  processed_at INTEGER, -- 处理时间
  created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),  -- 创建时间
  updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))  -- 更新时间
);

-- 创建充值统计表
CREATE TABLE IF NOT EXISTS recharge_statistics (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  date TEXT NOT NULL UNIQUE, -- 日期，格式为YYYY-MM-DD
  total_amount INTEGER NOT NULL DEFAULT 0, -- 当日总充值金额（人民币）
  request_count INTEGER NOT NULL DEFAULT 0, -- 当日充值请求数
  approved_count INTEGER NOT NULL DEFAULT 0, -- 当日批准充值数
  rejected_count INTEGER NOT NULL DEFAULT 0, -- 当日拒绝充值数
  updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')) -- 更新时间
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_recharge_requests_user_id ON recharge_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_recharge_requests_status ON recharge_requests(status);
CREATE INDEX IF NOT EXISTS idx_recharge_requests_created_at ON recharge_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_recharge_statistics_date ON recharge_statistics(date);