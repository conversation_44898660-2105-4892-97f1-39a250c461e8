package auth

import (
	"time"

	"github.com/google/uuid"
)

// User 用户领域模型
type User struct {
	ID        uuid.UUID `json:"id"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Subject 科目模型
type Subject struct {
	Key         string `json:"key"`         // prompt_key
	Description string `json:"description"` // description
}

// LoginRequest 登录请求
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// RecoverRequest 密码恢复请求
type RecoverRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// VerifyRequest 验证请求
type VerifyRequest struct {
	Email string `json:"email" binding:"required,email"`
	Token string `json:"token" binding:"required"`
	Type  string `json:"type" binding:"required"`
}

// UpdatePasswordRequest 更新密码请求
type UpdatePasswordRequest struct {
	Password string `json:"password" binding:"required,min=6"`
}

// EmailCodeRequest 邮箱验证码请求
type EmailCodeRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// EmailLoginRequest 邮箱验证码登录请求
type EmailLoginRequest struct {
	Email string `json:"email" binding:"required,email"`
	Code  string `json:"code" binding:"required"`
}

// AuthResponse 认证响应
type AuthResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresIn    int       `json:"expires_in"`
	ExpiresAt    int64     `json:"expires_at"`
	User         *User     `json:"user,omitempty"`
	Subjects     []Subject `json:"subjects,omitempty"` // 支持的科目列表
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// UserListRequest 用户列表请求
type UserListRequest struct {
	Page    int `form:"page"`
	PerPage int `form:"perPage"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users   []User `json:"users"`
	Count   int    `json:"count"`
	Success bool   `json:"success"`
}
