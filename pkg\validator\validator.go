package validator

import (
	"fmt"
	"regexp"
	"serverless-aig/pkg/errors"
	"strings"
	"time"
)

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) error {
	if email == "" {
		return errors.NewValidationError("email", "邮箱不能为空")
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return errors.NewValidationError("email", "邮箱格式不正确")
	}

	return nil
}

// ValidatePassword 验证密码强度
func ValidatePassword(password string) error {
	if password == "" {
		return errors.NewValidationError("password", "密码不能为空")
	}

	if len(password) < 6 {
		return errors.NewValidationError("password", "密码长度不能少于6位")
	}

	if len(password) > 128 {
		return errors.NewValidationError("password", "密码长度不能超过128位")
	}

	return nil
}

// ValidateRequired 验证必填字段
func ValidateRequired(value, fieldName string) error {
	if strings.TrimSpace(value) == "" {
		return errors.NewValidationError(fieldName, fieldName+"不能为空")
	}
	return nil
}

// ValidatePositiveInt 验证正整数
func ValidatePositiveInt(value int, fieldName string) error {
	if value <= 0 {
		return errors.NewValidationError(fieldName, fieldName+"必须大于0")
	}
	return nil
}

// ValidatePositiveInt64 验证正整数64
func ValidatePositiveInt64(value int64, fieldName string) error {
	if value <= 0 {
		return errors.NewValidationError(fieldName, fieldName+"必须大于0")
	}
	return nil
}

// ValidateDate 验证日期格式
func ValidateDate(dateStr, fieldName string) error {
	if dateStr == "" {
		return errors.NewValidationError(fieldName, fieldName+"不能为空")
	}

	_, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return errors.NewValidationError(fieldName, fieldName+"格式错误，应为YYYY-MM-DD")
	}

	return nil
}

// ValidateDateRange 验证日期范围
func ValidateDateRange(startDate, endDate, fieldName string) error {
	if err := ValidateDate(startDate, "开始日期"); err != nil {
		return err
	}

	if err := ValidateDate(endDate, "结束日期"); err != nil {
		return err
	}

	start, _ := time.Parse("2006-01-02", startDate)
	end, _ := time.Parse("2006-01-02", endDate)

	if end.Before(start) {
		return errors.NewValidationError(fieldName, "结束日期不能早于开始日期")
	}

	return nil
}

// ValidateEnum 验证枚举值
func ValidateEnum(value string, validValues []string, fieldName string) error {
	if value == "" {
		return errors.NewValidationError(fieldName, fieldName+"不能为空")
	}

	for _, valid := range validValues {
		if value == valid {
			return nil
		}
	}

	return errors.NewValidationError(fieldName, fieldName+"值无效")
}

// ValidateURL 验证URL格式
func ValidateURL(url, fieldName string) error {
	if url == "" {
		return nil // URL可以为空
	}

	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return errors.NewValidationError(fieldName, fieldName+"必须以http://或https://开头")
	}

	return nil
}

// ValidateLength 验证字符串长度
func ValidateLength(value string, minLen, maxLen int, fieldName string) error {
	length := len(value)

	if length < minLen {
		return errors.NewValidationError(fieldName, fmt.Sprintf("%s长度不能少于%d位", fieldName, minLen))
	}

	if maxLen > 0 && length > maxLen {
		return errors.NewValidationError(fieldName, fmt.Sprintf("%s长度不能超过%d位", fieldName, maxLen))
	}

	return nil
}
