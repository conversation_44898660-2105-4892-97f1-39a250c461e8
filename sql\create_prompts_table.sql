-- 创建系统提示词表
CREATE TABLE IF NOT EXISTS system_prompts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  prompt_key TEXT NOT NULL UNIQUE,  -- 提示词的唯一标识符
  prompt_text TEXT NOT NULL,        -- 提示词内容
  description TEXT,                 -- 提示词描述
  category TEXT,                    -- 提示词分类
  version INTEGER NOT NULL DEFAULT 1, -- 提示词版本
  is_active BOOLEAN NOT NULL DEFAULT 1, -- 是否激活
  created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),  -- 创建时间戳
  updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))   -- 最后更新时间戳
);

-- 在prompt_key上创建索引以加快查询速度
CREATE INDEX IF NOT EXISTS idx_system_prompts_key ON system_prompts(prompt_key);

-- 在category上创建索引以便按分类查询
CREATE INDEX IF NOT EXISTS idx_system_prompts_category ON system_prompts(category);
