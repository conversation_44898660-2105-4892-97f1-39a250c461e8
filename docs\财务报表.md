# 财务报表

## 月度营收 (Monthly Revenue)

营收只来自于消耗的付费积分。

```sql
-- 月度营收（单位：元）
SELECT 
    -- 消耗的付费积分是负数，所以要取反再求和
    SUM(-paid_points_change) / 1000.0 AS "月度营收_元"
FROM 
    transactions
WHERE 
    source = 'CONSUMPTION'
    AND paid_points_change < 0 -- 只统计消耗付费积分的交易
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

## 月度用户增长 (Monthly User Growth)

```sql
-- 月度用户增长
SELECT 
    COUNT(DISTINCT user_id) AS "月度用户增长_人"
FROM 
    transactions
WHERE 
    source = 'GIFT_SIGNUP'
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

## 月度充值总额 (Total Recharges)

```sql
-- 月度充值总额（单位：元）
SELECT 
    SUM(amount) AS "月度充值总额_元"
FROM 
    recharge_requests
WHERE 
    status = 'approved'
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

## 营销成本 - 赠送积分消耗 (Marketing Cost)

```sql
-- 营销成本（单位：元）
SELECT 
    SUM(free_points_change) / 1000.0 AS "营销成本_元"
FROM 
    transactions
WHERE 
    source = 'CONSUMPTION'
    AND free_points_change < 0 -- 只统计消耗赠送积分的交易
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

## 预收账款变化 (Change in Deferred Revenue)

预收账款 = 所有用户的 paid_balance 之和。月度变化 = 月度新增付费积分 - 月度消耗付费积分。

```sql
-- 月度预收账款净变化（单位：元）
SELECT
    SUM(paid_points_change) / 1000.0 AS "预收账款净变化_元"
FROM
    transactions
WHERE
    -- 考虑充值(增加)和消费(减少)
    source IN ('RECHARGE', 'CONSUMPTION') 
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
-- 正数表示预收账款增加，负数表示减少
```

## 综合月度报表 (Comprehensive Monthly Report)

包括:

- 营收
- 充值额
- 消耗的总积分 (付费+赠送)
- 营销成本 (本月送出总积分)
- 预收账款净变化

## 生成包含成本和利润的月度/年度财务报表

我们可以编写一个强大的SQL查询，它能自动计算按权责发生制分摊到当月的运营成本，并计算出最终的利润。

### 月度报表

假设我们要查询 2023年10月 的完整财务报表。

```sql
WITH report_period AS (
    -- 1. 定义报表周期，方便复用
    SELECT 
        '2023-10-01'::date AS start_date,
        '2023-11-01'::date AS end_date
),
monthly_revenue AS (
    -- 2. 计算月度营收 (来自消耗的付费积分)
    -- 这个部分保持不变
    SELECT 
        COALESCE(SUM(-paid_points_change), 0) / 1000.0 AS total_revenue
    FROM transactions, report_period
    WHERE 
        source = 'CONSUMPTION'
        AND paid_points_change < 0
        AND created_at >= report_period.start_date
        AND created_at < report_period.end_date
),
operational_expenses AS (
    -- 3. 计算月度运营成本 (按权责发生制分摊)
    SELECT
        COALESCE(SUM(
            -- (总金额 / 服务总天数) * 在本月的天数
            amount::numeric / (service_period_end - service_period_start + 1) * 
            GREATEST(0, (LEAST(report_period.end_date, service_period_end + 1) - GREATEST(report_period.start_date, service_period_start))::integer)
        ), 0) / 100.0 AS total_opex -- 除以100转为元
    FROM expenses, report_period
    WHERE
        -- 筛选出所有与报表周期有重叠的支出记录
        service_period_start < report_period.end_date
        AND service_period_end >= report_period.start_date
)
-- 4. 最终汇总报表 (简化版)
SELECT
    r.total_revenue AS "月度营收 (元)",
    o.total_opex AS "月度运营成本 (元)",
    (r.total_revenue - o.total_opex) AS "月度利润 (元)"
FROM
    monthly_revenue r,
    operational_expenses o;
```

### 年度报表

生成年度报表非常简单，只需修改 report_period 的时间范围即可：

```sql
-- 将 WITH report_period AS (...) 中的日期改为：
...
SELECT 
    '2023-01-01'::date AS start_date,
    '2024-01-01'::date AS end_date
...
-- 其他所有代码保持不变！
```

### 按类别分析支出

按类别查看本月各项运营成本的明细：

```sql
-- 查询2023年10月的各项支出明细
WITH report_period AS (
    SELECT '2023-10-01'::date AS start_date, '2023-11-01'::date AS end_date
)
SELECT
    category,
    SUM(
        amount::numeric / (service_period_end - service_period_start + 1) * 
        GREATEST(0, (LEAST(report_period.end_date, service_period_end + 1) - GREATEST(report_period.start_date, service_period_start))::integer)
    ) / 100.0 AS "当月成本_元"
FROM expenses, report_period
WHERE
    service_period_start < report_period.end_date
    AND service_period_end >= report_period.start_date
GROUP BY category
ORDER BY "当月成本_元" DESC;
```

### 辅助分析指标

衡量用户活跃度和营销活动效果的运营指标 (Operational Metric)

```sql
-- 查询2023年10月用户消耗的赠送积分情况
SELECT
    COUNT(DISTINCT user_id) AS "使用赠送积分的用户数",
    SUM(-free_points_change) AS "消耗的赠送积分总量",
    SUM(-free_points_change) / 1000.0 AS "消耗的赠送积分等值金额 (元)"
FROM
    transactions
WHERE
    source = 'CONSUMPTION'
    AND free_points_change < 0
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

这个查询结果可以帮助你回答问题，例如：

- “我们这个月的营销活动（赠送积分）带来了多少用户活跃？”
- “用户消耗的赠送积分价值，与我们当月API费用的增长是否匹配？”

### 如何记录不同类型的支出（示例）

例1：支付服务器年费 1200 元

```sql
INSERT INTO expenses (description, amount, category, vendor, transaction_date, service_period_start, service_period_end)
VALUES ('阿里云ECS服务器 2核4G 一年', 120000, 'SERVER_HOSTING', '阿里云', '2023-01-05', '2023-01-01', '2023-12-31');
```

例2：支付10月份的云函数月费 88 元

```sql
INSERT INTO expenses (description, amount, category, vendor, transaction_date, service_period_start, service_period_end)
VALUES ('10月份腾讯云函数月费', 8800, 'SERVER_HOSTING', '腾讯云', '2023-10-15', '2023-10-01', '2023-10-31');
```

例3：充值第三方API费用 300 元

```sql
INSERT INTO expenses (description, amount, category, vendor, transaction_date, service_period_start, service_period_end)
VALUES ('10月份火山引擎API调用费', 30000, 'API_SERVICES', '火山引擎', '2023-10-20', '2023-10-01', '2023-10-31');
```
