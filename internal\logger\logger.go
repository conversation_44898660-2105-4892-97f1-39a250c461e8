package logger

import (
	"context"
	"fmt"
	"log"
	"serverless-aig/internal/config"
	"sync"
	"time"

	"github.com/iamxvbaba/openobserve"
)

// LogLevel 定义日志级别
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug"
	LogLevelInfo  LogLevel = "info"
	LogLevelWarn  LogLevel = "warn"
	LogLevelError LogLevel = "error"
)

// Logger 封装日志发送功能
type Logger struct {
	openObLog *openobserve.OpenObLog
	appName   string
	jobName   string
	source    string
	ctx       context.Context
	cancel    context.CancelFunc
}

var (
	instance *Logger
	once     sync.Once
)

// New 创建新的日志实例
func New(cfg config.LogConfig) *Logger {
	once.Do(func() {
		ctx, cancel := context.WithCancel(context.Background())
		var openObLog *openobserve.OpenObLog

		if cfg.ServerURL != "" && cfg.Token != "" {
			openObLog = openobserve.New(ctx, cfg.ServerURL,
				openobserve.WithFullSize(100),
				openobserve.WithCompress(true),
				openobserve.WithWaitTime(time.Second*30),
				openobserve.WithRequestTimeout(time.Second*5),
				openobserve.WithIndexName("aig", true),
				openobserve.WithAuthorization(cfg.Token))
		}

		instance = &Logger{
			openObLog: openObLog,
			appName:   "serverless-aig",
			jobName:   "main",
			source:    "go-app",
			ctx:       ctx,
			cancel:    cancel,
		}
	})
	return instance
}

// LogInfo 记录信息级别日志
func (l *Logger) LogInfo(format string, args ...any) {
	l.logMessage(LogLevelInfo, format, args...)
}

// LogError 记录错误级别日志
func (l *Logger) LogError(format string, args ...any) {
	l.logMessage(LogLevelError, format, args...)
}

// LogWarn 记录警告级别日志
func (l *Logger) LogWarn(format string, args ...any) {
	l.logMessage(LogLevelWarn, format, args...)
}

// LogDebug 记录调试级别日志
func (l *Logger) LogDebug(format string, args ...any) {
	l.logMessage(LogLevelDebug, format, args...)
}

// logMessage 内部日志记录方法
func (l *Logger) logMessage(level LogLevel, format string, args ...any) {
	message := fmt.Sprintf(format, args...)

	// 控制台输出
	log.Printf("[%s] %s", level, message)

	// 远程日志服务
	if l.openObLog != nil {
		logData := map[string]any{
			"level":     string(level),
			"message":   message,
			"app_name":  l.appName,
			"job_name":  l.jobName,
			"source":    l.source,
			"timestamp": time.Now().Unix(),
		}

		select {
		case <-l.ctx.Done():
			return
		default:
			l.openObLog.Send(logData)
		}
	}
}

// Cleanup 清理日志资源
func (l *Logger) Cleanup() {
	if l.cancel != nil {
		l.cancel()
	}
	if l.openObLog != nil {
		l.cancel()
		time.Sleep(3 * time.Second)
	}
}

// 全局日志函数，保持向后兼容
func LogInfo(format string, args ...any) {
	if instance != nil {
		instance.LogInfo(format, args...)
	} else {
		log.Printf("[INFO] "+format, args...)
	}
}

func LogError(format string, args ...any) {
	if instance != nil {
		instance.LogError(format, args...)
	} else {
		log.Printf("[ERROR] "+format, args...)
	}
}

func LogWarn(format string, args ...any) {
	if instance != nil {
		instance.LogWarn(format, args...)
	} else {
		log.Printf("[WARN] "+format, args...)
	}
}

func LogDebug(format string, args ...any) {
	if instance != nil {
		instance.LogDebug(format, args...)
	} else {
		log.Printf("[DEBUG] "+format, args...)
	}
}
