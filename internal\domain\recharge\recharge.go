package recharge

import (
	"time"

	"github.com/google/uuid"
)

// ApprovalStatus 充值状态枚举
type ApprovalStatus string

const (
	StatusPending  ApprovalStatus = "pending"
	StatusApproved ApprovalStatus = "approved"
	StatusRejected ApprovalStatus = "rejected"
)

// RechargeRequest 充值申请领域模型
type RechargeRequest struct {
	ID            int            `json:"id" db:"id"`
	UserID        uuid.UUID      `json:"user_id" db:"user_id"`
	Amount        float64        `json:"amount" db:"amount"`                   // 充值金额（人民币，单位：元）
	PointsToGrant int64          `json:"points_to_grant" db:"points_to_grant"` // 根据充值金额换算出的积分
	OrderID       string         `json:"order_id" db:"order_id"`
	Status        ApprovalStatus `json:"status" db:"status"`
	PaymentMethod string         `json:"payment_method" db:"payment_method"`
	PaymentProof  *string        `json:"payment_proof" db:"payment_proof"`
	AdminNote     *string        `json:"admin_note" db:"admin_note"`
	ProcessedBy   *uuid.UUID     `json:"processed_by" db:"processed_by"`
	ProcessedAt   *time.Time     `json:"processed_at" db:"processed_at"`
	CreatedAt     time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at" db:"updated_at"`
}

// UserRechargeRequest 用户充值申请请求
type UserRechargeRequest struct {
	Amount       int    `json:"amount" binding:"required,min=1"`  // 充值金额（人民币,单位：元）
	OrderID      string `json:"order_id" binding:"required"`      // 支付宝订单号
	PaymentProof string `json:"payment_proof" binding:"required"` // 支付凭证URL
}

// AdminProcessRequest 管理员处理充值申请请求
type AdminProcessRequest struct {
	RequestID int            `json:"request_id" binding:"required"`
	Action    ApprovalStatus `json:"action" binding:"required,oneof=approved rejected"`
	AdminNote string         `json:"admin_note"`
	RmbAmount *float64       `json:"rmb_amount"` // 管理员可修改的实际充值金额
}

// RechargeListRequest 充值列表请求
type RechargeListRequest struct {
	Status string `form:"status"`
	Limit  int    `form:"limit"`
	Offset int    `form:"offset"`
}

// RechargeListResponse 充值列表响应
type RechargeListResponse struct {
	Requests []RechargeRequest `json:"requests"`
	Count    int               `json:"count"`
	Total    int               `json:"total"`
}

// UserRechargeResponse 用户充值申请响应
type UserRechargeResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

// AdminProcessResponse 管理员处理响应
type AdminProcessResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

// IsProcessed 检查充值申请是否已处理
func (r *RechargeRequest) IsProcessed() bool {
	return r.Status == StatusApproved || r.Status == StatusRejected
}

// CanProcess 检查充值申请是否可以处理
func (r *RechargeRequest) CanProcess() bool {
	return r.Status == StatusPending
}
