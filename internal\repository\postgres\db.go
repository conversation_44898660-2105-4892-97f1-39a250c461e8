package postgres

import (
	"fmt"
	"serverless-aig/internal/config"
	"serverless-aig/internal/logger"
	"time"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
)

// DB PostgreSQL数据库连接
type DB struct {
	conn   *sqlx.DB
	logger *logger.Logger
}

// New 创建新的数据库连接
func New(cfg config.DatabaseConfig, logger *logger.Logger) *DB {
	db := &DB{
		logger: logger,
	}

	conn, err := db.connect(cfg)
	if err != nil {
		panic(fmt.Sprintf("数据库连接失败: %v", err))
	}

	db.conn = conn
	return db
}

// connect 连接数据库
func (db *DB) connect(cfg config.DatabaseConfig) (*sqlx.DB, error) {
	if cfg.Host == "" {
		return nil, fmt.Errorf("DB_HOST environment variable not set")
	}

	// 构建DSN
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.Name)

	// 创建连接
	conn, err := sqlx.Connect("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 配置连接池参数（适配serverless环境）
	conn.SetMaxOpenConns(cfg.MaxOpenConns)
	conn.SetMaxIdleConns(cfg.MaxIdleConns)
	conn.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)
	conn.SetConnMaxIdleTime(time.Duration(cfg.ConnMaxIdleTime) * time.Second)

	// 测试连接
	if err := conn.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return conn, nil
}

// GetConn 获取数据库连接
func (db *DB) GetConn() *sqlx.DB {
	return db.conn
}

// Ping 测试数据库连接
func (db *DB) Ping() error {
	return db.conn.Ping()
}

// Close 关闭数据库连接
func (db *DB) Close() error {
	if db.conn != nil {
		return db.conn.Close()
	}
	return nil
}

// BeginTx 开始事务
func (db *DB) BeginTx() (*sqlx.Tx, error) {
	return db.conn.Beginx()
}

// WithTx 在事务中执行操作
func (db *DB) WithTx(fn func(*sqlx.Tx) error) error {
	tx, err := db.BeginTx()
	if err != nil {
		return err
	}

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()

	err = fn(tx)
	return err
}
