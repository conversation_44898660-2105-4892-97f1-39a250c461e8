package app

import (
	"fmt"
	"serverless-aig/internal/config"
	"serverless-aig/internal/container"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/router"

	"github.com/gin-gonic/gin"
)

// App 应用程序结构
type App struct {
	config    *config.Config
	logger    *logger.Logger
	container *container.Container
	router    *gin.Engine
}

// New 创建新的应用实例
func New(cfg *config.Config, log *logger.Logger) *App {
	// 创建依赖注入容器
	container := container.New(cfg, log)

	// 设置Gin模式
	if !cfg.Server.SwaggerEnabled {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	r := router.New(cfg, container)

	return &App{
		config:    cfg,
		logger:    log,
		container: container,
		router:    r,
	}
}

// Run 启动应用
func (a *App) Run() error {
	// 确保资源正确清理
	defer a.container.Cleanup()

	// 启动服务器
	serverAddr := fmt.Sprintf("%s:%d", a.config.Server.Host, a.config.Server.Port)
	a.logger.LogInfo("服务器启动在 %s", serverAddr)

	switch a.config.ServiceMode {
	case "admin":
		a.logger.LogInfo("当前服务模式: 管理员模式")
	case "user":
		a.logger.LogInfo("当前服务模式: 用户模式")
	default:
		a.logger.LogInfo("当前服务模式: 默认模式 (所有路由)")
	}

	return a.router.Run(serverAddr)
}

// Health 健康检查
func (a *App) Health() error {
	// 检查数据库连接
	if err := a.container.DB().Ping(); err != nil {
		return fmt.Errorf("数据库连接失败: %w", err)
	}

	return nil
}

// Shutdown 优雅关闭
func (a *App) Shutdown() error {
	a.logger.LogInfo("应用正在关闭...")
	a.container.Cleanup()
	return nil
}
