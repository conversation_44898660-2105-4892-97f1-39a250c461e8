package response

import (
	"net/http"
	"serverless-aig/pkg/errors"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Success bool   `json:"success"`
	Data    any    `json:"data,omitempty"`
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
	Code    string `json:"code,omitempty"`
}

// Success 成功响应
func Success(c *gin.Context, data any) {
	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    data,
	})
}

// SuccessWithMessage 带消息的成功响应
func SuccessWithMessage(c *gin.Context, data any, message string) {
	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    data,
		Message: message,
	})
}

// Error 错误响应
func Error(c *gin.Context, statusCode int, err error) {
	response := Response{
		Success: false,
		Error:   err.Error(),
	}

	// 根据错误类型设置不同的响应
	switch e := err.(type) {
	case *errors.BusinessError:
		response.Code = e.Code
		response.Message = e.Message
		if e.Details != "" {
			response.Error = e.Details
		}
	case *errors.ValidationError:
		response.Code = "VALIDATION_ERROR"
		response.Message = "请求参数验证失败"
	case *errors.DatabaseError:
		response.Code = "DATABASE_ERROR"
		response.Message = "数据库操作失败"
		if statusCode == 0 {
			statusCode = http.StatusInternalServerError
		}
	case *errors.ExternalServiceError:
		response.Code = "EXTERNAL_SERVICE_ERROR"
		response.Message = "外部服务调用失败"
		if statusCode == 0 {
			statusCode = http.StatusServiceUnavailable
		}
	}

	// 设置默认状态码
	if statusCode == 0 {
		statusCode = http.StatusInternalServerError
	}

	c.JSON(statusCode, response)
}

// BadRequest 400错误响应
func BadRequest(c *gin.Context, err error) {
	Error(c, http.StatusBadRequest, err)
}

// Unauthorized 401错误响应
func Unauthorized(c *gin.Context, err error) {
	Error(c, http.StatusUnauthorized, err)
}

// Forbidden 403错误响应
func Forbidden(c *gin.Context, err error) {
	Error(c, http.StatusForbidden, err)
}

// NotFound 404错误响应
func NotFound(c *gin.Context, err error) {
	Error(c, http.StatusNotFound, err)
}

// Conflict 409错误响应
func Conflict(c *gin.Context, err error) {
	Error(c, http.StatusConflict, err)
}

// InternalServerError 500错误响应
func InternalServerError(c *gin.Context, err error) {
	Error(c, http.StatusInternalServerError, err)
}

// ServiceUnavailable 503错误响应
func ServiceUnavailable(c *gin.Context, err error) {
	Error(c, http.StatusServiceUnavailable, err)
}

// ValidationError 验证错误响应
func ValidationError(c *gin.Context, field, message string) {
	err := errors.NewValidationError(field, message)
	BadRequest(c, err)
}

// BusinessError 业务错误响应
func BusinessError(c *gin.Context, statusCode int, code, message string) {
	err := errors.NewBusinessError(code, message)
	Error(c, statusCode, err)
}

// DatabaseError 数据库错误响应
func DatabaseError(c *gin.Context, operation string, err error) {
	dbErr := errors.NewDatabaseError(operation, "数据库操作失败", err)
	InternalServerError(c, dbErr)
}

// ExternalServiceError 外部服务错误响应
func ExternalServiceError(c *gin.Context, service string, err error) {
	extErr := errors.NewExternalServiceError(service, "外部服务调用失败", err)
	ServiceUnavailable(c, extErr)
}
