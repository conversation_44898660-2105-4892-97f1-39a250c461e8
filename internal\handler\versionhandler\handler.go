package versionhandler

import (
	"serverless-aig/internal/config"
	"serverless-aig/pkg/response"

	"github.com/gin-gonic/gin"
)

// Handler 版本处理器
type Handler struct {
	config *config.Config
}

// New 创建版本处理器
func New(cfg *config.Config) *Handler {
	return &Handler{
		config: cfg,
	}
}

// GetVersion 获取应用版本信息
// @Summary 获取应用版本信息
// @Description 获取当前应用的版本信息，包括版本号、构建时间等
// @Tags 系统
// @Produce json
// @Success 200 {object} config.AppConfig "应用版本信息，包含版本号和构建信息"
// @Router /api/v1/version [get]
func (h *Handler) GetVersion(c *gin.Context) {
	response.Success(c, h.config.App)
}
