package wallethandler

import (
	"strconv"

	"serverless-aig/internal/domain/wallet"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/middleware"
	"serverless-aig/pkg/response"

	walletservice "serverless-aig/internal/service/wallet"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// Handler 钱包处理器
type Handler struct {
	walletService walletservice.Service
	logger        *logger.Logger
}

// New 创建钱包处理器
func New(walletService walletservice.Service, logger *logger.Logger) *Handler {
	return &Handler{
		walletService: walletService,
		logger:        logger,
	}
}

// GetBalance 获取钱包余额
// @Summary 查询钱包余额
// @Description 获取当前用户的钱包余额信息，包括付费积分和免费积分
// @Tags 用户钱包
// @Security BearerAuth
// @Produce json
// @Success 200 {object} wallet.BalanceResponse "余额信息，包含付费积分、免费积分和总余额"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/wallet/balance [get]
func (h *Handler) GetBalance(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	if user == nil {
		response.Unauthorized(c, nil)
		return
	}

	balance, err := h.walletService.GetBalance(c.Request.Context(), user.ID)
	if err != nil {
		h.logger.LogError("获取钱包余额失败: %v", err)
		response.InternalServerError(c, err)
		return
	}

	response.Success(c, balance)
}

// GetTransactions 获取交易记录
// @Summary 查询交易记录
// @Description 获取当前用户的交易记录列表，包括充值、消费、赠送等所有交易类型
// @Tags 用户钱包
// @Security BearerAuth
// @Produce json
// @Param limit query int false "每页数量，最大100" default(20) minimum(1) maximum(100)
// @Param offset query int false "偏移量，用于分页" default(0) minimum(0)
// @Success 200 {object} wallet.TransactionsResponse "交易记录列表，包含交易详情和分页信息"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/wallet/transactions [get]
func (h *Handler) GetTransactions(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	if user == nil {
		response.Unauthorized(c, nil)
		return
	}

	// 获取分页参数
	limit := 20
	offset := 0

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	transactions, err := h.walletService.GetTransactions(c.Request.Context(), user.ID, limit, offset)
	if err != nil {
		h.logger.LogError("获取交易记录失败: %v", err)
		response.InternalServerError(c, err)
		return
	}

	response.Success(c, transactions)
}

// AdminAdjustBalance 管理员调整用户余额
// @Summary 调整用户余额
// @Description 管理员手动调整用户余额，仅用于添加免费积分或赠送积分，不支持扣减操作
// @Tags 管理员用户
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body wallet.AdjustBalanceRequest true "调整余额请求" example({"user_id":"123e4567-e89b-12d3-a456-426614174000","amount":1000,"description":"活动赠送积分"})
// @Success 200 {object} models.SwaggerEmptyResponse "余额调整成功"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如用户ID格式不正确或金额无效"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 403 {object} models.SwaggerForbiddenErrorResponse "权限不足，需要管理员权限"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/admin/user/balance/adjust [post]
func (h *Handler) AdminAdjustBalance(c *gin.Context) {
	var req wallet.AdjustBalanceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	// 解析用户ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		response.ValidationError(c, "user_id", "无效的用户ID格式")
		return
	}

	// 调整余额
	updatedWallet, err := h.walletService.AdjustBalance(c.Request.Context(), userID, req.Amount, req.Description)
	if err != nil {
		h.logger.LogError("调整用户余额失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, map[string]any{
		"user_id":       userID,
		"paid_balance":  updatedWallet.PaidBalance,
		"free_balance":  updatedWallet.FreeBalance,
		"total_balance": updatedWallet.TotalBalance(),
	}, "余额调整成功")
}
