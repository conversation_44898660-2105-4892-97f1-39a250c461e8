package aihandler

import (
	"serverless-aig/internal/domain/ai"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/middleware"
	aiservice "serverless-aig/internal/service/ai"
	"serverless-aig/pkg/response"

	"github.com/gin-gonic/gin"
)

// Handler AI处理器
type Handler struct {
	aiService aiservice.Service
	logger    *logger.Logger
}

// New 创建AI处理器
func New(aiService aiservice.Service, logger *logger.Logger) *Handler {
	return &Handler{
		aiService: aiService,
		logger:    logger,
	}
}

// Analysis AI图像分析
// @Summary AI图像分析
// @Description 使用AI模型分析图像内容，支持多种分析模式：专业模式(OCR+分析)、智能模式、经济模式
// @Tags 人工智能
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body ai.AnalysisRequest true "分析请求" example({"text":"请分析这道数学题的解答","prompt_key":"math","content":"https://example.com/image.jpg","analysis_mode":"standard"})
// @Success 200 {object} ai.AnalysisResponse "分析结果，包含学生答案、得分和详细评分说明"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如图像URL无效或分析模式不支持"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 402 {object} models.SwaggerInsufficientBalanceErrorResponse "余额不足，无法完成分析"
// @Failure 500 {object} models.SwaggerServiceUnavailableErrorResponse "服务器内部错误或AI服务不可用"
// @Router /api/v2/chat/analysis [post]
func (h *Handler) Analysis(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	if user == nil {
		response.Unauthorized(c, nil)
		return
	}

	var req ai.AnalysisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	result, err := h.aiService.Analysis(c.Request.Context(), user.ID, &req)
	if err != nil {
		h.logger.LogError("AI分析失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.Success(c, result)
}

// GetConfigs 获取配置列表
// @Summary 获取配置列表
// @Description 获取系统AI分析配置列表，包含可用的分析模式和参数设置
// @Tags 人工智能
// @Security BearerAuth
// @Produce json
// @Success 200 {object} []ai.ConfigItem "AI配置列表，包含模式名称、描述和参数"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/ai/configs [get]
func (h *Handler) GetConfigs(c *gin.Context) {
	configs, err := h.aiService.GetConfigs(c.Request.Context())
	if err != nil {
		h.logger.LogError("获取配置列表失败: %v", err)
		response.InternalServerError(c, err)
		return
	}

	response.Success(c, configs)
}

// GetSubjects 获取学科列表
// @Summary 获取学科列表
// @Description 获取系统支持的学科列表，用于AI分析时选择对应的评分标准
// @Tags 人工智能
// @Security BearerAuth
// @Produce json
// @Success 200 {object} []ai.Subject "学科列表，包含学科键值和描述信息"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/ai/subjects [get]
func (h *Handler) GetSubjects(c *gin.Context) {
	subjects, err := h.aiService.GetSubjects(c.Request.Context())
	if err != nil {
		h.logger.LogError("获取学科列表失败: %v", err)
		response.InternalServerError(c, err)
		return
	}

	response.Success(c, subjects)
}
