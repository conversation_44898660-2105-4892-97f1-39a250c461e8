package wallet

import (
	"time"

	"github.com/google/uuid"
)

// Wallet 钱包领域模型
type Wallet struct {
	ID          int       `json:"id" db:"id"`
	UserID      uuid.UUID `json:"user_id" db:"user_id"`
	PaidBalance int64     `json:"paid_balance" db:"paid_balance"` // 付费积分余额
	FreeBalance int64     `json:"free_balance" db:"free_balance"` // 赠送积分余额
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// TransactionSource 交易来源枚举
type TransactionSource string

const (
	TransactionSourceRecharge    TransactionSource = "RECHARGE"    // 付费充值
	TransactionSourceConsumption TransactionSource = "CONSUMPTION" // 业务消费
	TransactionSourceGiftSignup  TransactionSource = "GIFT_SIGNUP" // 注册赠送
	TransactionSourceGiftPromo   TransactionSource = "GIFT_PROMO"  // 活动赠送
	TransactionSourceRefund      TransactionSource = "REFUND"      // 退款
)

// Transaction 交易记录领域模型
type Transaction struct {
	ID                   int64             `json:"id" db:"id"`
	UserID               uuid.UUID         `json:"user_id" db:"user_id"`
	Source               TransactionSource `json:"source" db:"source"`
	PaidPointsChange     int64             `json:"paid_points_change" db:"paid_points_change"`
	FreePointsChange     int64             `json:"free_points_change" db:"free_points_change"`
	PaidBalanceAfter     int64             `json:"paid_balance_after" db:"paid_balance_after"`
	FreeBalanceAfter     int64             `json:"free_balance_after" db:"free_balance_after"`
	Description          *string           `json:"description" db:"description"`
	RelatedRechargeID    *int              `json:"related_recharge_id" db:"related_recharge_id"`
	RelatedConsumptionID *string           `json:"related_consumption_id" db:"related_consumption_id"`
	Metadata             *string           `json:"metadata" db:"metadata"`
	CreatedAt            time.Time         `json:"created_at" db:"created_at"`
}

// BalanceResponse 余额响应
type BalanceResponse struct {
	PaidBalance  int64 `json:"paid_balance"`
	FreeBalance  int64 `json:"free_balance"`
	TotalBalance int64 `json:"total_balance"`
}

// TransactionsResponse 交易记录响应
type TransactionsResponse struct {
	Transactions []Transaction `json:"transactions"`
	Count        int           `json:"count"`
	Total        int           `json:"total"`
}

// AdjustBalanceRequest 调整余额请求
type AdjustBalanceRequest struct {
	UserID      string `json:"user_id" binding:"required"`
	Amount      int64  `json:"amount" binding:"required"`
	Description string `json:"description"`
}

// ConsumePointsRequest 消费积分请求
type ConsumePointsRequest struct {
	UserID               uuid.UUID `json:"user_id"`
	Amount               int64     `json:"amount"`
	Description          string    `json:"description"`
	RelatedConsumptionID string    `json:"related_consumption_id"`
}

// TotalBalance 计算总余额
func (w *Wallet) TotalBalance() int64 {
	return w.PaidBalance + w.FreeBalance
}

// CanConsume 检查是否可以消费指定金额
func (w *Wallet) CanConsume(amount int64) bool {
	return w.TotalBalance() >= amount
}
