package rechargehandler

import (
	"fmt"
	"strconv"

	"serverless-aig/internal/domain/recharge"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/middleware"
	"serverless-aig/pkg/response"

	rechargeservice "serverless-aig/internal/service/recharge"

	"github.com/gin-gonic/gin"
)

// Handler 充值处理器
type Handler struct {
	rechargeService rechargeservice.Service
	logger          *logger.Logger
}

// New 创建充值处理器
func New(rechargeService rechargeservice.Service, logger *logger.Logger) *Handler {
	return &Handler{
		rechargeService: rechargeService,
		logger:          logger,
	}
}

// UserRequest 用户提交充值申请
// @Summary 提交充值申请
// @Description 用户提交充值申请，需要提供支付凭证和订单信息，按1:1000比例转换为积分
// @Tags 用户充值
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body recharge.UserRechargeRequest true "充值申请信息" example({"amount":100,"order_id":"20240101123456","payment_proof":"https://example.com/proof.jpg"})
// @Success 200 {object} models.SwaggerEmptyResponse "充值申请提交成功，等待管理员审核"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如金额无效或支付凭证格式不正确"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/recharge/request [post]
func (h *Handler) UserRequest(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	if user == nil {
		response.Unauthorized(c, nil)
		return
	}

	var req recharge.UserRechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	err := h.rechargeService.UserCreateRequest(c.Request.Context(), user.ID, &req)
	if err != nil {
		h.logger.LogError("用户提交充值申请失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, nil, "充值申请提交成功，请等待管理员审核")
}

// UserList 用户获取充值申请列表
// @Summary 获取用户充值申请列表
// @Description 获取当前用户的充值申请列表，包含申请状态、金额、处理时间等信息
// @Tags 用户充值
// @Security BearerAuth
// @Produce json
// @Param limit query int false "每页数量，最大100" default(20) minimum(1) maximum(100)
// @Param offset query int false "偏移量，用于分页" default(0) minimum(0)
// @Success 200 {object} recharge.RechargeListResponse "充值申请列表，包含申请详情和分页信息"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/recharge/list [get]
func (h *Handler) UserList(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	if user == nil {
		response.Unauthorized(c, nil)
		return
	}

	// 获取分页参数
	limit := 20
	offset := 0

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	resp, err := h.rechargeService.UserGetRequests(c.Request.Context(), user.ID, limit, offset)
	if err != nil {
		h.logger.LogError("获取用户充值申请列表失败: %v", err)
		response.InternalServerError(c, err)
		return
	}

	response.Success(c, resp)
}

// AdminList 管理员获取充值申请列表
// @Summary 获取充值申请列表
// @Description 管理员获取所有用户的充值申请列表
// @Tags 管理员充值
// @Security BearerAuth
// @Produce json
// @Param status query string false "申请状态" Enums(pending,approved,rejected)
// @Param limit query int false "每页数量" default(50)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} recharge.RechargeListResponse "充值申请列表"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Router /api/v1/admin/recharge/requests [get]
func (h *Handler) AdminList(c *gin.Context) {
	// 获取查询参数
	status := recharge.ApprovalStatus(c.Query("status"))
	limit := 50
	offset := 0

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	// 验证状态参数
	if status != "" && status != recharge.StatusPending &&
		status != recharge.StatusApproved && status != recharge.StatusRejected {
		response.ValidationError(c, "status", "无效的申请状态")
		return
	}

	resp, err := h.rechargeService.AdminGetRequests(c.Request.Context(), status, limit, offset)
	if err != nil {
		h.logger.LogError("获取管理员充值申请列表失败: %v", err)
		response.InternalServerError(c, err)
		return
	}

	response.Success(c, resp)
}

// AdminProcess 管理员处理充值申请
// @Summary 处理充值申请
// @Description 管理员批准或拒绝充值申请，可以修改实际充值金额，批准后自动添加积分到用户钱包
// @Tags 管理员充值
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body recharge.AdminProcessRequest true "处理请求" example({"request_id":123,"action":"approved","admin_note":"审核通过","rmb_amount":100.0})
// @Success 200 {object} models.SwaggerEmptyResponse "充值申请处理成功"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如申请ID无效或操作类型不正确"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 403 {object} models.SwaggerForbiddenErrorResponse "权限不足，需要管理员权限"
// @Failure 404 {object} models.SwaggerNotFoundErrorResponse "充值申请不存在"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/admin/recharge/process [post]
func (h *Handler) AdminProcess(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	if user == nil {
		response.Unauthorized(c, nil)
		return
	}

	var req recharge.AdminProcessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	err := h.rechargeService.AdminProcessRequest(c.Request.Context(), user.ID, &req)
	if err != nil {
		h.logger.LogError("管理员处理充值申请失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	actionText := map[recharge.ApprovalStatus]string{
		recharge.StatusApproved: "批准",
		recharge.StatusRejected: "拒绝",
	}[req.Action]

	response.SuccessWithMessage(c, nil, fmt.Sprintf("充值申请%s成功", actionText))
}
