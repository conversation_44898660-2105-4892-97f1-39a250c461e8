package recharge

import (
	"context"
	"serverless-aig/internal/domain/recharge"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/internal/repository/postgres"
	"serverless-aig/pkg/errors"
	"time"

	walletservice "serverless-aig/internal/service/wallet"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
)

// Service 充值服务接口
type Service interface {
	UserCreateRequest(ctx context.Context, userID uuid.UUID, req *recharge.UserRechargeRequest) error
	UserGetRequests(ctx context.Context, userID uuid.UUID, limit, offset int) (*recharge.RechargeListResponse, error)
	AdminGetRequests(ctx context.Context, status recharge.ApprovalStatus, limit, offset int) (*recharge.RechargeListResponse, error)
	AdminProcessRequest(ctx context.Context, adminID uuid.UUID, req *recharge.AdminProcessRequest) error
}

// service 充值服务实现
type service struct {
	rechargeRepo  interfaces.RechargeRepository
	walletService walletservice.Service
	db            *postgres.DB
	logger        *logger.Logger
}

// NewService 创建充值服务
func NewService(db *postgres.DB, logger *logger.Logger) Service {
	return &service{
		rechargeRepo:  postgres.NewRechargeRepository(db),
		walletService: walletservice.NewService(db, logger),
		db:            db,
		logger:        logger,
	}
}

// UserCreateRequest 用户创建充值申请
func (s *service) UserCreateRequest(ctx context.Context, userID uuid.UUID, req *recharge.UserRechargeRequest) error {
	// 验证请求参数
	if req.Amount <= 0 {
		return errors.NewValidationError("amount", "充值金额必须大于0")
	}

	if req.OrderID == "" {
		return errors.NewValidationError("order_id", "订单号不能为空")
	}

	if req.PaymentProof == "" {
		return errors.NewValidationError("payment_proof", "支付凭证不能为空")
	}

	// 检查订单号是否已存在
	existing, err := s.rechargeRepo.GetRechargeRequestByOrderID(ctx, req.OrderID)
	if err != nil && err != errors.ErrNotFound {
		s.logger.LogError("检查订单号失败: %v", err)
		return err
	}

	if existing != nil {
		return errors.NewBusinessError("DUPLICATE_ORDER", "订单号已存在")
	}

	// 计算积分（1元 = 1000积分）
	pointsToGrant := int64(req.Amount) * 1000

	// 创建充值申请
	rechargeReq := &recharge.RechargeRequest{
		UserID:        userID,
		Amount:        float64(req.Amount),
		PointsToGrant: pointsToGrant,
		OrderID:       req.OrderID,
		Status:        recharge.StatusPending,
		PaymentMethod: "alipay",
		PaymentProof:  &req.PaymentProof,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	err = s.rechargeRepo.CreateRechargeRequest(ctx, rechargeReq)
	if err != nil {
		s.logger.LogError("创建充值申请失败: %v", err)
		return err
	}

	s.logger.LogInfo("用户 %s 创建充值申请，金额: %d元，订单号: %s", userID, req.Amount, req.OrderID)

	return nil
}

// UserGetRequests 用户获取充值申请列表
func (s *service) UserGetRequests(ctx context.Context, userID uuid.UUID, limit, offset int) (*recharge.RechargeListResponse, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	requests, total, err := s.rechargeRepo.GetUserRechargeRequests(ctx, userID, limit, offset)
	if err != nil {
		s.logger.LogError("获取用户充值申请列表失败: %v", err)
		return nil, err
	}

	return &recharge.RechargeListResponse{
		Requests: requests,
		Count:    len(requests),
		Total:    total,
	}, nil
}

// AdminGetRequests 管理员获取充值申请列表
func (s *service) AdminGetRequests(ctx context.Context, status recharge.ApprovalStatus, limit, offset int) (*recharge.RechargeListResponse, error) {
	if limit <= 0 {
		limit = 50
	}
	if limit > 100 {
		limit = 100
	}

	requests, total, err := s.rechargeRepo.GetAdminRechargeRequests(ctx, status, limit, offset)
	if err != nil {
		s.logger.LogError("获取管理员充值申请列表失败: %v", err)
		return nil, err
	}

	return &recharge.RechargeListResponse{
		Requests: requests,
		Count:    len(requests),
		Total:    total,
	}, nil
}

// AdminProcessRequest 管理员处理充值申请
func (s *service) AdminProcessRequest(ctx context.Context, adminID uuid.UUID, req *recharge.AdminProcessRequest) error {
	// 验证请求参数
	if req.Action != recharge.StatusApproved && req.Action != recharge.StatusRejected {
		return errors.NewValidationError("action", "无效的处理动作")
	}

	// 使用事务处理整个流程
	var rechargeReq *recharge.RechargeRequest
	err := s.db.WithTx(func(tx *sqlx.Tx) error {
		// 获取充值申请（带锁）
		var err error
		rechargeReq, err = s.rechargeRepo.GetRechargeRequestForUpdate(ctx, req.RequestID)
		if err != nil {
			s.logger.LogError("获取充值申请失败: %v", err)
			return err
		}

		// 检查申请状态
		if !rechargeReq.CanProcess() {
			return errors.NewBusinessError("INVALID_STATUS", "充值申请已处理，无法重复处理")
		}

		// 更新申请状态
		now := time.Now()
		rechargeReq.Status = req.Action
		rechargeReq.AdminNote = &req.AdminNote
		rechargeReq.ProcessedBy = &adminID
		rechargeReq.ProcessedAt = &now
		rechargeReq.UpdatedAt = now

		// 如果管理员修改了金额，更新相关字段
		if req.RmbAmount != nil && *req.RmbAmount > 0 {
			rechargeReq.Amount = *req.RmbAmount
			rechargeReq.PointsToGrant = int64(*req.RmbAmount * 1000) // 1元 = 1000积分
		}

		err = s.rechargeRepo.UpdateRechargeRequest(ctx, rechargeReq)
		if err != nil {
			s.logger.LogError("更新充值申请失败: %v", err)
			return err
		}

		// 如果批准，则增加用户余额
		if req.Action == recharge.StatusApproved {
			// 确保用户有钱包
			_, err := s.walletService.GetBalance(ctx, rechargeReq.UserID)
			if err != nil {
				s.logger.LogError("获取用户钱包失败: %v", err)
				return err
			}

			// 增加付费余额
			_, err = s.walletService.AddPaidBalance(ctx, rechargeReq.UserID, rechargeReq.PointsToGrant,
				"充值到账", &rechargeReq.ID)
			if err != nil {
				s.logger.LogError("增加付费余额失败: %v", err)
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}

	s.logger.LogInfo("管理员 %s %s 充值申请 %d，用户: %s，金额: %.2f元",
		adminID,
		map[recharge.ApprovalStatus]string{
			recharge.StatusApproved: "批准了",
			recharge.StatusRejected: "拒绝了",
		}[req.Action],
		req.RequestID,
		rechargeReq.UserID,
		rechargeReq.Amount)

	return nil
}
