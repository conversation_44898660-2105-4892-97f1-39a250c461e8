# Swagger API 文档集成

本项目已成功集成Swagger API文档，提供了完整的API接口文档和在线测试功能。

## 功能特性

- ✅ 完整的API文档生成
- ✅ 在线API测试界面
- ✅ 环境变量控制启用/禁用
- ✅ 支持认证令牌测试
- ✅ 分类组织的API端点

## 环境变量配置

在`.env`文件中添加以下配置：

```env
# Swagger文档配置
SWAGGER_ENABLED=true  # 设置为false可禁用Swagger文档
```

## 访问Swagger文档

当`SWAGGER_ENABLED=true`时，可以通过以下URL访问Swagger文档：

- **Swagger UI界面**: http://localhost:9000/swagger/index.html
- **JSON格式文档**: http://localhost:9000/swagger/doc.json
- **YAML格式文档**: http://localhost:9000/swagger/swagger.yaml

## API分类

文档中的API按以下分类组织：

### 系统相关
- `GET /` - 服务器状态
- `GET /api/v1/version` - 获取应用版本信息

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/refresh` - 刷新访问令牌
- `POST /api/v1/auth/recover` - 密码恢复
- `POST /api/v1/auth/verify` - 验证令牌
- `POST /api/v1/auth/update-password` - 更新密码
- `POST /api/v1/auth/email-code` - 发送邮箱验证码
- `POST /api/v1/auth/email-login` - 邮箱验证码登录

### 钱包相关（需要认证）
- `GET /api/v1/wallet/balance` - 查询钱包余额
- `GET /api/v1/wallet/transactions` - 查询交易记录

### AI服务（需要认证）
- `POST /api/v2/chat/analysis` - AI图像分析

### 充值相关（需要认证）
- `POST /api/v1/recharge/request` - 提交充值申请
- `GET /api/v1/recharge/list` - 查询充值申请列表

### 存储相关（需要认证）
- `GET /api/v1/tos/credentials` - 获取TOS临时凭证

### 管理员功能（需要管理员权限）
- 充值管理：`GET /api/v1/admin/recharge/requests`、`POST /api/v1/admin/recharge/process`
- 用户管理：`GET /api/v1/admin/user/info`、`POST /api/v1/admin/user/balance/adjust`、`GET /api/v1/admin/user/list`、`POST /api/v1/admin/user/create`
- 财务管理：`POST /api/v1/admin/finance/expense`、`GET /api/v1/admin/finance/report`、`GET /api/v1/admin/finance/analysis`、`GET /api/v1/admin/finance/expenses`

## 认证说明

需要认证的API端点使用Bearer Token认证方式：

1. 在Swagger UI中点击右上角的"Authorize"按钮
2. 在弹出的对话框中输入访问令牌（格式：`Bearer your_token_here`）
3. 点击"Authorize"完成认证设置

## 开发说明

### 生成Swagger文档

当修改API注释后，需要重新生成Swagger文档：

```bash
swag init
```

### 添加新的API注释

为新的API端点添加Swagger注释示例：

```go
// @Summary API摘要
// @Description API详细描述
// @Tags API分类
// @Security BearerAuth  // 如果需要认证
// @Accept json
// @Produce json
// @Param request body RequestStruct true "请求参数说明"
// @Success 200 {object} ResponseStruct "成功响应"
// @Failure 400 {object} ErrorResponse "错误响应"
// @Router /api/path [method]
func HandlerFunction(c *gin.Context) {
    // 处理逻辑
}
```

## 生产环境建议

在生产环境中，建议：

1. 设置`SWAGGER_ENABLED=false`禁用Swagger文档
2. 或者通过反向代理限制Swagger端点的访问权限
3. 确保敏感信息不会在API文档中暴露

## 故障排除

### 常见问题

1. **Swagger页面无法访问**
   - 检查`SWAGGER_ENABLED`环境变量是否设置为`true`
   - 确认服务器正常启动

2. **API文档不完整**
   - 运行`swag init`重新生成文档
   - 检查API注释格式是否正确

3. **认证测试失败**
   - 确认令牌格式正确（需要包含"Bearer "前缀）
   - 检查令牌是否有效且未过期
