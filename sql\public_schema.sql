--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Debian 17.5-1.pgdg120+1)
-- Dumped by pg_dump version 17.5 (Debian 17.5-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: pg_database_owner
--

CREATE SCHEMA public;


ALTER SCHEMA public OWNER TO pg_database_owner;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: pg_database_owner
--

COMMENT ON SCHEMA public IS 'standard public schema';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: app_configs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.app_configs (
    id bigint NOT NULL,
    config_key text,
    config_value text,
    created_at bigint,
    updated_at bigint
);


ALTER TABLE public.app_configs OWNER TO postgres;

--
-- Name: app_configs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.app_configs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.app_configs_id_seq OWNER TO postgres;

--
-- Name: app_configs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.app_configs_id_seq OWNED BY public.app_configs.id;


--
-- Name: recharge_requests; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.recharge_requests (
    id bigint NOT NULL,
    user_id text,
    amount bigint,
    order_id text,
    status text DEFAULT 'pending'::text,
    payment_method text DEFAULT 'alipay'::text,
    payment_proof text,
    admin_note text,
    processed_by text,
    processed_at bigint,
    created_at bigint,
    updated_at bigint
);


ALTER TABLE public.recharge_requests OWNER TO postgres;

--
-- Name: recharge_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.recharge_requests_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.recharge_requests_id_seq OWNER TO postgres;

--
-- Name: recharge_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.recharge_requests_id_seq OWNED BY public.recharge_requests.id;


--
-- Name: recharge_statistics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.recharge_statistics (
    id bigint NOT NULL,
    date text,
    total_amount bigint DEFAULT '0'::bigint,
    request_count bigint DEFAULT '0'::bigint,
    approved_count bigint DEFAULT '0'::bigint,
    rejected_count bigint DEFAULT '0'::bigint,
    updated_at bigint
);


ALTER TABLE public.recharge_statistics OWNER TO postgres;

--
-- Name: recharge_statistics_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.recharge_statistics_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.recharge_statistics_id_seq OWNER TO postgres;

--
-- Name: recharge_statistics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.recharge_statistics_id_seq OWNED BY public.recharge_statistics.id;


--
-- Name: system_prompts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.system_prompts (
    id bigint NOT NULL,
    prompt_key text,
    prompt_text text,
    description text,
    category text,
    version bigint DEFAULT '1'::bigint,
    is_active boolean DEFAULT true,
    created_at bigint,
    updated_at bigint
);


ALTER TABLE public.system_prompts OWNER TO postgres;

--
-- Name: system_prompts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.system_prompts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.system_prompts_id_seq OWNER TO postgres;

--
-- Name: system_prompts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.system_prompts_id_seq OWNED BY public.system_prompts.id;


--
-- Name: transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.transactions (
    id bigint NOT NULL,
    user_id text,
    type text,
    amount bigint,
    balance_after bigint,
    description text,
    metadata text,
    created_at bigint
);


ALTER TABLE public.transactions OWNER TO postgres;

--
-- Name: transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.transactions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.transactions_id_seq OWNER TO postgres;

--
-- Name: transactions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.transactions_id_seq OWNED BY public.transactions.id;


--
-- Name: wallets; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wallets (
    id bigint NOT NULL,
    user_id text,
    balance bigint DEFAULT '0'::bigint,
    created_at bigint,
    updated_at bigint
);


ALTER TABLE public.wallets OWNER TO postgres;

--
-- Name: wallets_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.wallets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.wallets_id_seq OWNER TO postgres;

--
-- Name: wallets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.wallets_id_seq OWNED BY public.wallets.id;


--
-- Name: app_configs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.app_configs ALTER COLUMN id SET DEFAULT nextval('public.app_configs_id_seq'::regclass);


--
-- Name: recharge_requests id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.recharge_requests ALTER COLUMN id SET DEFAULT nextval('public.recharge_requests_id_seq'::regclass);


--
-- Name: recharge_statistics id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.recharge_statistics ALTER COLUMN id SET DEFAULT nextval('public.recharge_statistics_id_seq'::regclass);


--
-- Name: system_prompts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.system_prompts ALTER COLUMN id SET DEFAULT nextval('public.system_prompts_id_seq'::regclass);


--
-- Name: transactions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transactions ALTER COLUMN id SET DEFAULT nextval('public.transactions_id_seq'::regclass);


--
-- Name: wallets id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wallets ALTER COLUMN id SET DEFAULT nextval('public.wallets_id_seq'::regclass);


--
-- Name: app_configs idx_16475_app_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.app_configs
    ADD CONSTRAINT idx_16475_app_configs_pkey PRIMARY KEY (id);


--
-- Name: system_prompts idx_16482_system_prompts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.system_prompts
    ADD CONSTRAINT idx_16482_system_prompts_pkey PRIMARY KEY (id);


--
-- Name: wallets idx_16491_wallets_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wallets
    ADD CONSTRAINT idx_16491_wallets_pkey PRIMARY KEY (id);


--
-- Name: transactions idx_16499_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT idx_16499_transactions_pkey PRIMARY KEY (id);


--
-- Name: recharge_requests idx_16506_recharge_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.recharge_requests
    ADD CONSTRAINT idx_16506_recharge_requests_pkey PRIMARY KEY (id);


--
-- Name: recharge_statistics idx_16515_recharge_statistics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.recharge_statistics
    ADD CONSTRAINT idx_16515_recharge_statistics_pkey PRIMARY KEY (id);


--
-- Name: idx_16475_sqlite_autoindex_app_configs_1; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_16475_sqlite_autoindex_app_configs_1 ON public.app_configs USING btree (config_key);


--
-- Name: idx_16482_idx_system_prompts_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16482_idx_system_prompts_category ON public.system_prompts USING btree (category);


--
-- Name: idx_16482_idx_system_prompts_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16482_idx_system_prompts_key ON public.system_prompts USING btree (prompt_key);


--
-- Name: idx_16482_sqlite_autoindex_system_prompts_1; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_16482_sqlite_autoindex_system_prompts_1 ON public.system_prompts USING btree (prompt_key);


--
-- Name: idx_16491_idx_wallets_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16491_idx_wallets_user_id ON public.wallets USING btree (user_id);


--
-- Name: idx_16491_sqlite_autoindex_wallets_1; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_16491_sqlite_autoindex_wallets_1 ON public.wallets USING btree (user_id);


--
-- Name: idx_16499_idx_transactions_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16499_idx_transactions_created_at ON public.transactions USING btree (created_at);


--
-- Name: idx_16499_idx_transactions_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16499_idx_transactions_type ON public.transactions USING btree (type);


--
-- Name: idx_16499_idx_transactions_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16499_idx_transactions_user_id ON public.transactions USING btree (user_id);


--
-- Name: idx_16506_idx_recharge_requests_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16506_idx_recharge_requests_created_at ON public.recharge_requests USING btree (created_at);


--
-- Name: idx_16506_idx_recharge_requests_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16506_idx_recharge_requests_status ON public.recharge_requests USING btree (status);


--
-- Name: idx_16506_idx_recharge_requests_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16506_idx_recharge_requests_user_id ON public.recharge_requests USING btree (user_id);


--
-- Name: idx_16506_sqlite_autoindex_recharge_requests_1; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_16506_sqlite_autoindex_recharge_requests_1 ON public.recharge_requests USING btree (order_id);


--
-- Name: idx_16515_idx_recharge_statistics_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_16515_idx_recharge_statistics_date ON public.recharge_statistics USING btree (date);


--
-- Name: idx_16515_sqlite_autoindex_recharge_statistics_1; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX idx_16515_sqlite_autoindex_recharge_statistics_1 ON public.recharge_statistics USING btree (date);


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT CREATE ON SCHEMA public TO aig_user;


--
-- Name: TABLE app_configs; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.app_configs TO aig_user;


--
-- Name: SEQUENCE app_configs_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.app_configs_id_seq TO aig_user;


--
-- Name: TABLE recharge_requests; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.recharge_requests TO aig_user;


--
-- Name: SEQUENCE recharge_requests_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.recharge_requests_id_seq TO aig_user;


--
-- Name: TABLE recharge_statistics; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.recharge_statistics TO aig_user;


--
-- Name: SEQUENCE recharge_statistics_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.recharge_statistics_id_seq TO aig_user;


--
-- Name: TABLE system_prompts; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.system_prompts TO aig_user;


--
-- Name: SEQUENCE system_prompts_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.system_prompts_id_seq TO aig_user;


--
-- Name: TABLE transactions; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.transactions TO aig_user;


--
-- Name: SEQUENCE transactions_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.transactions_id_seq TO aig_user;


--
-- Name: TABLE wallets; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.wallets TO aig_user;


--
-- Name: SEQUENCE wallets_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.wallets_id_seq TO aig_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO aig_user;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS TO aig_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES TO aig_user;


--
-- PostgreSQL database dump complete
--

