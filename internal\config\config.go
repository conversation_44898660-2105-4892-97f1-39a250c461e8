package config

import (
	"os"
	"strconv"
	"strings"
)

// Config 应用配置
type Config struct {
	Server      ServerConfig     `json:"server"`
	Database    DatabaseConfig   `json:"database"`
	Supabase    SupabaseConfig   `json:"supabase"`
	Volcengine  VolcengineConfig `json:"volcengine"`
	TOS         TOSConfig        `json:"tos"`
	Log         LogConfig        `json:"log"`
	App         AppConfig        `json:"app"`
	Email       EmailConfig      `json:"email"`
	ServiceMode string           `json:"service_mode"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host           string `json:"host"`
	Port           int    `json:"port"`
	SwaggerEnabled bool   `json:"swagger_enabled"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host            string `json:"host"`
	Port            string `json:"port"`
	User            string `json:"user"`
	Password        string `json:"password"`
	Name            string `json:"name"`
	MaxOpenConns    int    `json:"max_open_conns"`
	MaxIdleConns    int    `json:"max_idle_conns"`
	ConnMaxLifetime int    `json:"conn_max_lifetime"`
	ConnMaxIdleTime int    `json:"conn_max_idle_time"`
}

// SupabaseConfig Supabase配置
type SupabaseConfig struct {
	ID  string `json:"id"`
	URL string `json:"url"`
	Key string `json:"key"`
}

// VolcengineConfig 火山引擎配置
type VolcengineConfig struct {
	APIKey string `json:"api_key"`
}

// TOSConfig 对象存储配置
type TOSConfig struct {
	STSBaseURL       string `json:"sts_base_url"`
	AccessKey        string `json:"access_key"`
	SecretKey        string `json:"secret_key"`
	RoleArn          string `json:"role_arn"`
	Bucket           string `json:"bucket"`
	Endpoint         string `json:"endpoint"`
	IntranetEndpoint string `json:"intranet_endpoint"`
	Region           string `json:"region"`
}

// LogConfig 日志配置
type LogConfig struct {
	ServerURL string `json:"server_url"`
	Token     string `json:"token"`
}

// AppConfig 应用配置
type AppConfig struct {
	Version     string   `json:"version"`
	DownloadURL string   `json:"download_url"`
	ForceUpdate bool     `json:"force_update"`
	UpdateLog   string   `json:"update_log"`
	AdminIDs    []string `json:"admin_ids"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	UseSmtp  bool   `json:"use_smtp"`
	Host     string `json:"host"`
	Port     string `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	From     string `json:"from"`
}

// Load 加载配置
func Load(serviceMode string) *Config {
	return &Config{
		Server: ServerConfig{
			Host:           getEnvWithDefault("SERVER_HOST", "0.0.0.0"),
			Port:           getEnvIntWithDefault("SERVER_PORT", 9000),
			SwaggerEnabled: getEnvBoolWithDefault("SWAGGER_ENABLED", true),
		},
		Database: DatabaseConfig{
			Host:            getEnvWithDefault("DB_HOST", ""),
			Port:            getEnvWithDefault("DB_PORT", "5432"),
			User:            getEnvWithDefault("DB_USER", ""),
			Password:        getEnvWithDefault("DB_PASSWORD", ""),
			Name:            getEnvWithDefault("DB_NAME", ""),
			MaxOpenConns:    3,
			MaxIdleConns:    1,
			ConnMaxLifetime: 30,
			ConnMaxIdleTime: 10,
		},
		Supabase: SupabaseConfig{
			ID:  getEnvWithDefault("SUPABASE_ID", ""),
			URL: getEnvWithDefault("SUPABASE_URL", ""),
			Key: getEnvWithDefault("SUPABASE_KEY", ""),
		},
		Volcengine: VolcengineConfig{
			APIKey: getEnvWithDefault("ARK_API_KEY", ""),
		},
		TOS: TOSConfig{
			STSBaseURL:       getEnvWithDefault("VOLC_STS_BASE_URL", "https://sts.volcengineapi.com"),
			AccessKey:        getEnvWithDefault("VOLC_ACCESS_KEY", ""),
			SecretKey:        getEnvWithDefault("VOLC_SECRET_KEY", ""),
			RoleArn:          getEnvWithDefault("VOLC_ROLE_ARN", ""),
			Bucket:           getEnvWithDefault("VOLC_BUCKET", ""),
			Endpoint:         getEnvWithDefault("VOLC_ENDPOINT", ""),
			IntranetEndpoint: getEnvWithDefault("VOLC_INTRANET_ENDPOINT", ""),
			Region:           getEnvWithDefault("VOLC_REGION", ""),
		},
		Log: LogConfig{
			ServerURL: getEnvWithDefault("LOG_SERVER_URL", "http://192.168.192.43:5080"),
			Token:     getEnvWithDefault("LOG_SERVER_TOKEN", ""),
		},
		App: AppConfig{
			Version:     getEnvWithDefault("APP_VERSION", "v0.0.0"),
			DownloadURL: getEnvWithDefault("APP_DOWNLOAD_URL", "https://shanzhulab.cn/"),
			ForceUpdate: getEnvBoolWithDefault("APP_FORCE_UPDATE", false),
			UpdateLog:   getEnvWithDefault("APP_UPDATE_LOG", "在山竹阅卷官网https://shanzhulab.cn或者公众号获取最新版本下载链接"),
			AdminIDs:    getEnvSliceWithDefault("ADMIN_IDS", []string{"333323c8-b917-403c-8d63-8e6e6f646522", "dd05b2aa-b079-437a-8db9-5345835f68aa"}),
		},
		Email: EmailConfig{
			UseSmtp:  getEnvBoolWithDefault("USE_SMTP_MAIL", false),
			Host:     getEnvWithDefault("SMTP_HOST", ""),
			Port:     getEnvWithDefault("SMTP_PORT", ""),
			User:     getEnvWithDefault("SMTP_USER", ""),
			Password: getEnvWithDefault("SMTP_PASSWORD", ""),
			From:     getEnvWithDefault("SMTP_FROM", ""),
		},
		ServiceMode: serviceMode,
	}
}

// 辅助函数
func getEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvIntWithDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBoolWithDefault(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvSliceWithDefault(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}
