# 管理员用户管理 API 文档

本文档描述了管理员用户管理功能的 API 接口。

## 管理员用户管理 API

### 1. 获取用户列表

**请求**:

- 方法: `GET`
- 路径: `/api/v1/admin/user/list?limit=50&offset=0`
- 需要认证: 是（管理员）

**参数**:

- `limit`: 每页数量，默认 50
- `offset`: 偏移量，默认 0

**响应**:

```json
{
  "success": true,
  "users": [
    {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "created_at": 1633506000,
      "last_sign_in_at": 1633506100
    }
  ],
  "count": 1,
  "total": 10
}
```

### 2. 创建用户

**请求**:

- 方法: `POST`
- 路径: `/api/v1/admin/user/create`
- 需要认证: 是（管理员）

**请求体**:

```json
{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**响应**:

```json
{
  "success": true,
  "message": "用户创建成功",
  "user_id": "new-user-uuid"
}
```

**错误响应**:

- 400 Bad Request: 请求格式错误或缺少必要参数
- 401 Unauthorized: 未授权，需要登录
- 403 Forbidden: 权限不足，不是管理员
- 500 Internal Server Error: 服务器内部错误

### 3. 获取用户信息

**请求**:

- 方法: `GET`
- 路径: `/api/v1/admin/user/info?user_id=user-uuid`
- 需要认证: 是（管理员）

**参数**:

- `user_id`: 用户 ID（必填）

**响应**:

```json
{
  "user_id": "user-uuid",
  "balance": 200000, // 积分余额
  "total_recharge": 300 // 总充值金额（人民币）
}
```

## 状态码说明

- 200: 请求成功
- 400: 请求参数错误
- 401: 未授权，需要登录
- 403: 权限不足，不是管理员
- 500: 服务器内部错误

## 用户管理流程说明

1. 管理员可以查看系统中所有用户的列表
2. 管理员可以创建新的用户账号，指定邮箱和初始密码
3. 管理员可以查看特定用户的详细信息，包括积分余额和充值金额
4. 用户创建后，可以使用指定的邮箱和密码登录系统
