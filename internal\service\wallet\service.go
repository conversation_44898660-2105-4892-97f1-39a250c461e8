package wallet

import (
	"context"
	"serverless-aig/internal/domain/wallet"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/internal/repository/postgres"
	"serverless-aig/pkg/errors"

	"github.com/google/uuid"
)

// Service 钱包服务接口
type Service interface {
	GetBalance(ctx context.Context, userID uuid.UUID) (*wallet.BalanceResponse, error)
	GetTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) (*wallet.TransactionsResponse, error)
	ConsumePoints(ctx context.Context, req *wallet.ConsumePointsRequest) (*wallet.Wallet, error)
	AdjustBalance(ctx context.Context, userID uuid.UUID, amount int64, description string) (*wallet.Wallet, error)
	AddPaidBalance(ctx context.Context, userID uuid.UUID, amount int64, description string, relatedRechargeID *int) (*wallet.Wallet, error)
	CreateWalletForNewUser(ctx context.Context, userID uuid.UUID) (*wallet.Wallet, error)
}

// service 钱包服务实现
type service struct {
	walletRepo interfaces.WalletRepository
	logger     *logger.Logger
}

// NewService 创建钱包服务
func NewService(db *postgres.DB, logger *logger.Logger) Service {
	return &service{
		walletRepo: postgres.NewWalletRepository(db),
		logger:     logger,
	}
}

// GetBalance 获取余额
func (s *service) GetBalance(ctx context.Context, userID uuid.UUID) (*wallet.BalanceResponse, error) {
	w, err := s.walletRepo.GetWallet(ctx, userID)
	if err != nil {
		if err == errors.ErrUserNotFound {
			// 用户钱包不存在，创建新钱包
			w, err = s.CreateWalletForNewUser(ctx, userID)
			if err != nil {
				s.logger.LogError("创建用户钱包失败: %v", err)
				return nil, err
			}
		} else {
			s.logger.LogError("获取钱包信息失败: %v", err)
			return nil, err
		}
	}

	return &wallet.BalanceResponse{
		PaidBalance:  w.PaidBalance,
		FreeBalance:  w.FreeBalance,
		TotalBalance: w.TotalBalance(),
	}, nil
}

// GetTransactions 获取交易记录
func (s *service) GetTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) (*wallet.TransactionsResponse, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	transactions, total, err := s.walletRepo.GetTransactions(ctx, userID, limit, offset)
	if err != nil {
		s.logger.LogError("获取交易记录失败: %v", err)
		return nil, err
	}

	return &wallet.TransactionsResponse{
		Transactions: transactions,
		Count:        len(transactions),
		Total:        total,
	}, nil
}

// ConsumePoints 消费积分
func (s *service) ConsumePoints(ctx context.Context, req *wallet.ConsumePointsRequest) (*wallet.Wallet, error) {
	if req.Amount <= 0 {
		return nil, errors.NewValidationError("amount", "消费金额必须大于0")
	}

	// 检查用户钱包是否存在
	existingWallet, err := s.walletRepo.GetWallet(ctx, req.UserID)
	if err != nil {
		if err == errors.ErrUserNotFound {
			return nil, errors.NewBusinessError("WALLET_NOT_FOUND", "用户钱包不存在")
		}
		s.logger.LogError("获取钱包信息失败: %v", err)
		return nil, err
	}

	// 检查余额是否足够
	if !existingWallet.CanConsume(req.Amount) {
		return nil, errors.ErrInsufficientBalance
	}

	// 执行消费
	updatedWallet, err := s.walletRepo.ConsumePoints(ctx, req)
	if err != nil {
		s.logger.LogError("消费积分失败: %v", err)
		return nil, err
	}

	s.logger.LogInfo("用户 %s 消费积分 %d，剩余余额: 付费=%d, 免费=%d",
		req.UserID, req.Amount, updatedWallet.PaidBalance, updatedWallet.FreeBalance)

	return updatedWallet, nil
}

// AdjustBalance 调整余额（仅用于添加免费积分）
func (s *service) AdjustBalance(ctx context.Context, userID uuid.UUID, amount int64, description string) (*wallet.Wallet, error) {
	if amount <= 0 {
		return nil, errors.NewValidationError("amount", "调整金额必须大于0")
	}

	// 检查用户钱包是否存在
	_, err := s.walletRepo.GetWallet(ctx, userID)
	if err != nil {
		if err == errors.ErrUserNotFound {
			return nil, errors.NewBusinessError("WALLET_NOT_FOUND", "用户钱包不存在")
		}
		s.logger.LogError("获取钱包信息失败: %v", err)
		return nil, err
	}

	// 执行余额调整
	updatedWallet, err := s.walletRepo.AdjustBalance(ctx, userID, amount, description)
	if err != nil {
		s.logger.LogError("调整余额失败: %v", err)
		return nil, err
	}

	s.logger.LogInfo("管理员为用户 %s 调整余额 +%d，描述: %s", userID, amount, description)

	return updatedWallet, nil
}

// AddPaidBalance 增加付费余额
func (s *service) AddPaidBalance(ctx context.Context, userID uuid.UUID, amount int64, description string, relatedRechargeID *int) (*wallet.Wallet, error) {
	if amount <= 0 {
		return nil, errors.NewValidationError("amount", "增加金额必须大于0")
	}

	// 检查用户钱包是否存在
	_, err := s.walletRepo.GetWallet(ctx, userID)
	if err != nil {
		if err == errors.ErrUserNotFound {
			return nil, errors.NewBusinessError("WALLET_NOT_FOUND", "用户钱包不存在")
		}
		s.logger.LogError("获取钱包信息失败: %v", err)
		return nil, err
	}

	// 执行付费余额增加
	updatedWallet, err := s.walletRepo.AddPaidBalance(ctx, userID, amount, description, relatedRechargeID)
	if err != nil {
		s.logger.LogError("增加付费余额失败: %v", err)
		return nil, err
	}

	s.logger.LogInfo("为用户 %s 增加付费余额 +%d，描述: %s", userID, amount, description)

	return updatedWallet, nil
}

// CreateWalletForNewUser 为新用户创建钱包
func (s *service) CreateWalletForNewUser(ctx context.Context, userID uuid.UUID) (*wallet.Wallet, error) {
	w, err := s.walletRepo.CreateWallet(ctx, userID)
	if err != nil {
		s.logger.LogError("创建用户钱包失败: %v", err)
		return nil, err
	}

	s.logger.LogInfo("为新用户 %s 创建钱包，初始余额: 付费=%d, 免费=%d",
		userID, w.PaidBalance, w.FreeBalance)

	return w, nil
}
