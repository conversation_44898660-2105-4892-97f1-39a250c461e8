package postgres

import (
	"context"
	"database/sql"
	"serverless-aig/internal/domain/finance"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/pkg/errors"
)

// financeRepository 财务仓储实现
type financeRepository struct {
	db *DB
}

// NewFinanceRepository 创建财务仓储
func NewFinanceRepository(db *DB) interfaces.FinanceRepository {
	return &financeRepository{db: db}
}

// CreateExpense 创建支出记录
func (r *financeRepository) CreateExpense(ctx context.Context, expense *finance.Expense) error {
	query := `INSERT INTO expenses 
			  (description, amount, currency, category, vendor, 
			   transaction_date, service_period_start, service_period_end, 
			   payment_method, invoice_url, recorded_by) 
			  VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`

	_, err := r.db.conn.ExecContext(ctx, query,
		expense.Description, expense.Amount, expense.Currency, expense.Category,
		expense.Vendor, expense.TransactionDate, expense.ServicePeriodStart,
		expense.ServicePeriodEnd, expense.PaymentMethod, expense.InvoiceURL,
		expense.RecordedBy)
	if err != nil {
		return errors.NewDatabaseError("CreateExpense", "创建支出记录失败", err)
	}

	return nil
}

// GetExpense 获取支出记录
func (r *financeRepository) GetExpense(ctx context.Context, id int64) (*finance.Expense, error) {
	query := `SELECT id, description, amount, currency, category, vendor,
			  transaction_date, service_period_start, service_period_end,
			  payment_method, invoice_url, recorded_by, created_at
			  FROM expenses WHERE id = $1`

	var expense finance.Expense
	err := r.db.conn.GetContext(ctx, &expense, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrNotFound
		}
		return nil, errors.NewDatabaseError("GetExpense", "获取支出记录失败", err)
	}

	return &expense, nil
}

// UpdateExpense 更新支出记录
func (r *financeRepository) UpdateExpense(ctx context.Context, expense *finance.Expense) error {
	query := `UPDATE expenses 
			  SET description = $2, amount = $3, currency = $4, category = $5,
			      vendor = $6, transaction_date = $7, service_period_start = $8,
			      service_period_end = $9, payment_method = $10, invoice_url = $11
			  WHERE id = $1`

	_, err := r.db.conn.ExecContext(ctx, query,
		expense.ID, expense.Description, expense.Amount, expense.Currency,
		expense.Category, expense.Vendor, expense.TransactionDate,
		expense.ServicePeriodStart, expense.ServicePeriodEnd,
		expense.PaymentMethod, expense.InvoiceURL)
	if err != nil {
		return errors.NewDatabaseError("UpdateExpense", "更新支出记录失败", err)
	}

	return nil
}

// DeleteExpense 删除支出记录
func (r *financeRepository) DeleteExpense(ctx context.Context, id int64) error {
	query := `DELETE FROM expenses WHERE id = $1`

	_, err := r.db.conn.ExecContext(ctx, query, id)
	if err != nil {
		return errors.NewDatabaseError("DeleteExpense", "删除支出记录失败", err)
	}

	return nil
}

// GetExpenses 获取支出列表
func (r *financeRepository) GetExpenses(ctx context.Context, req *finance.ExpenseListRequest) ([]finance.Expense, int, error) {
	// 构建查询条件
	whereClause := "WHERE 1=1"
	args := []any{}
	argIndex := 1

	if req.Category != "" {
		whereClause += " AND category = $" + string(rune(argIndex+'0'))
		args = append(args, req.Category)
		argIndex++
	}

	if req.StartDate != "" {
		whereClause += " AND transaction_date >= $" + string(rune(argIndex+'0'))
		args = append(args, req.StartDate)
		argIndex++
	}

	if req.EndDate != "" {
		whereClause += " AND transaction_date <= $" + string(rune(argIndex+'0'))
		args = append(args, req.EndDate)
		argIndex++
	}

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM expenses " + whereClause
	var total int
	err := r.db.conn.GetContext(ctx, &total, countQuery, args...)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetExpenses", "获取支出记录总数失败", err)
	}

	// 获取支出列表
	query := `SELECT id, description, amount, currency, category, vendor,
			  transaction_date, service_period_start, service_period_end,
			  payment_method, invoice_url, recorded_by, created_at
			  FROM expenses ` + whereClause + `
			  ORDER BY transaction_date DESC LIMIT $` + string(rune(argIndex+'0')) + ` OFFSET $` + string(rune(argIndex+'1'))

	args = append(args, req.Limit, req.Offset)

	var expenses []finance.Expense
	err = r.db.conn.SelectContext(ctx, &expenses, query, args...)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetExpenses", "获取支出列表失败", err)
	}

	return expenses, total, nil
}

// GetExpensesByCategory 按类别获取支出
func (r *financeRepository) GetExpensesByCategory(ctx context.Context, category finance.ExpenseCategory, startDate, endDate string) ([]finance.Expense, error) {
	query := `SELECT id, description, amount, currency, category, vendor,
			  transaction_date, service_period_start, service_period_end,
			  payment_method, invoice_url, recorded_by, created_at
			  FROM expenses 
			  WHERE category = $1 AND transaction_date >= $2 AND transaction_date <= $3
			  ORDER BY transaction_date DESC`

	var expenses []finance.Expense
	err := r.db.conn.SelectContext(ctx, &expenses, query, category, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetExpensesByCategory", "按类别获取支出失败", err)
	}

	return expenses, nil
}

// GetExpensesByDateRange 按日期范围获取支出
func (r *financeRepository) GetExpensesByDateRange(ctx context.Context, startDate, endDate string) ([]finance.Expense, error) {
	query := `SELECT id, description, amount, currency, category, vendor,
			  transaction_date, service_period_start, service_period_end,
			  payment_method, invoice_url, recorded_by, created_at
			  FROM expenses 
			  WHERE transaction_date >= $1 AND transaction_date <= $2
			  ORDER BY transaction_date DESC`

	var expenses []finance.Expense
	err := r.db.conn.SelectContext(ctx, &expenses, query, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetExpensesByDateRange", "按日期范围获取支出失败", err)
	}

	return expenses, nil
}

// GetFinancialReport 获取财务报表
func (r *financeRepository) GetFinancialReport(ctx context.Context, startDate, endDate string) (*finance.FinancialReport, error) {
	// 获取收入数据
	revenueQuery := `
	SELECT 
		COALESCE(SUM(amount), 0) as total_revenue,
		COUNT(*) as recharge_count,
		COALESCE(AVG(amount), 0) as average_recharge
	FROM recharge_requests 
	WHERE status = 'approved' 
	AND created_at >= $1::date 
	AND created_at < $2::date + INTERVAL '1 day'`

	var revenue finance.RevenueReport
	err := r.db.conn.GetContext(ctx, &revenue, revenueQuery, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetFinancialReport", "获取收入数据失败", err)
	}

	// 获取支出数据
	expenseQuery := `
	SELECT 
		COALESCE(SUM(amount), 0) as total_expenses,
		COUNT(*) as expense_count
	FROM expenses 
	WHERE transaction_date >= $1::date 
	AND transaction_date <= $2::date`

	var expenseReport finance.ExpenseReport
	err = r.db.conn.GetContext(ctx, &expenseReport, expenseQuery, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetFinancialReport", "获取支出数据失败", err)
	}

	// 获取按类别分组的支出
	categoryQuery := `
	SELECT category, SUM(amount) as amount
	FROM expenses 
	WHERE transaction_date >= $1::date 
	AND transaction_date <= $2::date
	GROUP BY category`

	rows, err := r.db.conn.QueryContext(ctx, categoryQuery, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetFinancialReport", "获取分类支出数据失败", err)
	}
	defer rows.Close()

	expensesByCategory := make(map[finance.ExpenseCategory]int64)
	for rows.Next() {
		var category finance.ExpenseCategory
		var amount int64
		if err := rows.Scan(&category, &amount); err != nil {
			return nil, errors.NewDatabaseError("GetFinancialReport", "扫描分类支出数据失败", err)
		}
		expensesByCategory[category] = amount
	}
	expenseReport.ExpensesByCategory = expensesByCategory

	// 计算利润
	grossProfit := revenue.TotalRevenue*100 - expenseReport.TotalExpenses // 收入转换为分
	var profitMargin float64
	if revenue.TotalRevenue > 0 {
		profitMargin = float64(grossProfit) / float64(revenue.TotalRevenue*100) * 100
	}

	profit := finance.ProfitReport{
		GrossProfit:  grossProfit,
		ProfitMargin: profitMargin,
	}

	// TODO: 获取用户指标数据
	userMetrics := finance.UserMetricsReport{
		NewUsers:    0,
		ActiveUsers: 0,
		PayingUsers: 0,
	}

	return &finance.FinancialReport{
		Period: finance.ReportPeriod{
			StartDate: startDate,
			EndDate:   endDate,
		},
		Revenue:     revenue,
		Expenses:    expenseReport,
		Profit:      profit,
		UserMetrics: userMetrics,
	}, nil
}

// GetExpenseAnalysis 获取支出分析
func (r *financeRepository) GetExpenseAnalysis(ctx context.Context, startDate, endDate string) (*finance.ExpenseAnalysis, error) {
	// 获取总支出用于计算百分比
	totalQuery := `SELECT COALESCE(SUM(amount), 0) FROM expenses 
				   WHERE transaction_date >= $1::date AND transaction_date <= $2::date`
	var totalAmount int64
	err := r.db.conn.GetContext(ctx, &totalAmount, totalQuery, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetExpenseAnalysis", "获取总支出失败", err)
	}

	// 获取按类别分组的分析
	categoryQuery := `
	SELECT category, SUM(amount) as amount, COUNT(*) as count
	FROM expenses 
	WHERE transaction_date >= $1::date AND transaction_date <= $2::date
	GROUP BY category`

	rows, err := r.db.conn.QueryContext(ctx, categoryQuery, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetExpenseAnalysis", "获取分类分析失败", err)
	}
	defer rows.Close()

	categoryBreakdown := make(map[finance.ExpenseCategory]finance.CategoryAnalysis)
	for rows.Next() {
		var category finance.ExpenseCategory
		var amount int64
		var count int
		if err := rows.Scan(&category, &amount, &count); err != nil {
			return nil, errors.NewDatabaseError("GetExpenseAnalysis", "扫描分类分析数据失败", err)
		}

		var percentage float64
		if totalAmount > 0 {
			percentage = float64(amount) / float64(totalAmount) * 100
		}

		categoryBreakdown[category] = finance.CategoryAnalysis{
			Amount:     amount,
			Count:      count,
			Percentage: percentage,
		}
	}

	// 获取月度趋势
	monthlyTrend, err := r.GetMonthlyExpenses(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 获取供应商排名
	topVendors, err := r.GetExpensesByVendor(ctx, startDate, endDate, 10)
	if err != nil {
		return nil, err
	}

	return &finance.ExpenseAnalysis{
		CategoryBreakdown: categoryBreakdown,
		MonthlyTrend:      monthlyTrend,
		TopVendors:        topVendors,
	}, nil
}

// GetTotalExpenses 获取总支出
func (r *financeRepository) GetTotalExpenses(ctx context.Context, startDate, endDate string) (int64, error) {
	query := `SELECT COALESCE(SUM(amount), 0) FROM expenses 
			  WHERE transaction_date >= $1::date AND transaction_date <= $2::date`

	var total int64
	err := r.db.conn.GetContext(ctx, &total, query, startDate, endDate)
	if err != nil {
		return 0, errors.NewDatabaseError("GetTotalExpenses", "获取总支出失败", err)
	}

	return total, nil
}

// GetExpensesByVendor 获取按供应商分组的支出
func (r *financeRepository) GetExpensesByVendor(ctx context.Context, startDate, endDate string, limit int) ([]finance.VendorExpense, error) {
	query := `
	SELECT vendor, SUM(amount) as amount
	FROM expenses 
	WHERE transaction_date >= $1::date AND transaction_date <= $2::date
	GROUP BY vendor
	ORDER BY amount DESC
	LIMIT $3`

	var vendors []finance.VendorExpense
	err := r.db.conn.SelectContext(ctx, &vendors, query, startDate, endDate, limit)
	if err != nil {
		return nil, errors.NewDatabaseError("GetExpensesByVendor", "获取供应商支出失败", err)
	}

	return vendors, nil
}

// GetMonthlyExpenses 获取月度支出
func (r *financeRepository) GetMonthlyExpenses(ctx context.Context, startDate, endDate string) ([]finance.MonthlyExpense, error) {
	query := `
	SELECT 
		TO_CHAR(transaction_date, 'YYYY-MM') as month,
		SUM(amount) as amount
	FROM expenses 
	WHERE transaction_date >= $1::date AND transaction_date <= $2::date
	GROUP BY TO_CHAR(transaction_date, 'YYYY-MM')
	ORDER BY month`

	var monthly []finance.MonthlyExpense
	err := r.db.conn.SelectContext(ctx, &monthly, query, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetMonthlyExpenses", "获取月度支出失败", err)
	}

	return monthly, nil
}
