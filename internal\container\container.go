package container

import (
	"serverless-aig/internal/client/supabase"
	"serverless-aig/internal/client/volcengine"
	"serverless-aig/internal/config"
	"serverless-aig/internal/handler/aihandler"
	"serverless-aig/internal/handler/authhandler"
	"serverless-aig/internal/handler/financehandler"
	"serverless-aig/internal/handler/rechargehandler"
	"serverless-aig/internal/handler/toshandler"
	"serverless-aig/internal/handler/versionhandler"
	"serverless-aig/internal/handler/wallethandler"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/repository/postgres"
	"serverless-aig/internal/service/ai"
	"serverless-aig/internal/service/auth"
	"serverless-aig/internal/service/finance"
	"serverless-aig/internal/service/recharge"
	"serverless-aig/internal/service/wallet"
)

// Container 依赖注入容器
type Container struct {
	config *config.Config
	logger *logger.Logger

	// 客户端
	supabaseClient   *supabase.Client
	volcengineClient *volcengine.Client

	// 仓储
	db *postgres.DB

	// 服务
	authService     auth.Service
	walletService   wallet.Service
	rechargeService recharge.Service
	financeService  finance.Service
	aiService       ai.Service

	// 处理器
	authHandler     *authhandler.Handler
	walletHandler   *wallethandler.Handler
	rechargeHandler *rechargehandler.Handler
	financeHandler  *financehandler.Handler
	aiHandler       *aihandler.Handler
	tosHandler      *toshandler.Handler
	versionHandler  *versionhandler.Handler
}

// New 创建新的容器
func New(cfg *config.Config, log *logger.Logger) *Container {
	c := &Container{
		config: cfg,
		logger: log,
	}

	c.initClients()
	c.initRepositories()
	c.initServices()
	c.initHandlers()

	return c
}

// initClients 初始化客户端
func (c *Container) initClients() {
	c.supabaseClient = supabase.New(c.config.Supabase, c.config.ServiceMode)
	c.volcengineClient = volcengine.New(c.config.Volcengine)
}

// initRepositories 初始化仓储
func (c *Container) initRepositories() {
	c.db = postgres.New(c.config.Database, c.logger)
}

// initServices 初始化服务
func (c *Container) initServices() {
	c.walletService = wallet.NewService(c.db, c.logger)
	c.authService = auth.NewService(c.supabaseClient, c.db, c.walletService, c.logger)
	c.rechargeService = recharge.NewService(c.db, c.logger)
	c.financeService = finance.NewService(c.db, c.logger)
	c.aiService = ai.NewService(c.volcengineClient, c.walletService, c.db, c.logger)
}

// initHandlers 初始化处理器
func (c *Container) initHandlers() {
	c.authHandler = authhandler.New(c.authService, c.logger)
	c.walletHandler = wallethandler.New(c.walletService, c.logger)
	c.rechargeHandler = rechargehandler.New(c.rechargeService, c.logger)
	c.financeHandler = financehandler.New(c.financeService, c.logger)
	c.aiHandler = aihandler.New(c.aiService, c.logger)
	c.tosHandler = toshandler.New(c.config, c.logger)
	c.versionHandler = versionhandler.New(c.config)
}

// Cleanup 清理资源
func (c *Container) Cleanup() {
	if c.db != nil {
		c.db.Close()
	}
}

// 获取器方法
func (c *Container) Config() *config.Config { return c.config }
func (c *Container) Logger() *logger.Logger { return c.logger }
func (c *Container) DB() *postgres.DB       { return c.db }

// 客户端获取器
func (c *Container) SupabaseClient() *supabase.Client     { return c.supabaseClient }
func (c *Container) VolcengineClient() *volcengine.Client { return c.volcengineClient }

// 服务获取器
func (c *Container) AuthService() auth.Service         { return c.authService }
func (c *Container) WalletService() wallet.Service     { return c.walletService }
func (c *Container) RechargeService() recharge.Service { return c.rechargeService }
func (c *Container) FinanceService() finance.Service   { return c.financeService }
func (c *Container) AIService() ai.Service             { return c.aiService }

// 处理器获取器
func (c *Container) AuthHandler() *authhandler.Handler         { return c.authHandler }
func (c *Container) WalletHandler() *wallethandler.Handler     { return c.walletHandler }
func (c *Container) RechargeHandler() *rechargehandler.Handler { return c.rechargeHandler }
func (c *Container) FinanceHandler() *financehandler.Handler   { return c.financeHandler }
func (c *Container) AIHandler() *aihandler.Handler             { return c.aiHandler }
func (c *Container) TOSHandler() *toshandler.Handler           { return c.tosHandler }
func (c *Container) VersionHandler() *versionhandler.Handler   { return c.versionHandler }
