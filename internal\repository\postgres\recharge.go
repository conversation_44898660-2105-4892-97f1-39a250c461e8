package postgres

import (
	"context"
	"database/sql"
	"serverless-aig/internal/domain/recharge"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/pkg/errors"

	"github.com/google/uuid"
)

// rechargeRepository 充值仓储实现
type rechargeRepository struct {
	db *DB
}

// NewRechargeRepository 创建充值仓储
func NewRechargeRepository(db *DB) interfaces.RechargeRepository {
	return &rechargeRepository{db: db}
}

// CreateRechargeRequest 创建充值申请
func (r *rechargeRepository) CreateRechargeRequest(ctx context.Context, req *recharge.RechargeRequest) error {
	query := `INSERT INTO recharge_requests 
			  (user_id, amount, points_to_grant, order_id, status, payment_method, payment_proof) 
			  VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := r.db.conn.ExecContext(ctx, query,
		req.UserID, req.Amount, req.PointsToGrant, req.OrderID,
		req.Status, req.PaymentMethod, req.PaymentProof)
	if err != nil {
		return errors.NewDatabaseError("CreateRechargeRequest", "创建充值申请失败", err)
	}

	return nil
}

// GetRechargeRequest 获取充值申请
func (r *rechargeRepository) GetRechargeRequest(ctx context.Context, id int) (*recharge.RechargeRequest, error) {
	query := `SELECT id, user_id, amount, points_to_grant, order_id, status, 
			  payment_method, payment_proof, admin_note, processed_by, 
			  processed_at, created_at, updated_at 
			  FROM recharge_requests WHERE id = $1`

	var req recharge.RechargeRequest
	err := r.db.conn.GetContext(ctx, &req, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrNotFound
		}
		return nil, errors.NewDatabaseError("GetRechargeRequest", "获取充值申请失败", err)
	}

	return &req, nil
}

// GetRechargeRequestForUpdate 获取充值申请（带锁）
func (r *rechargeRepository) GetRechargeRequestForUpdate(ctx context.Context, id int) (*recharge.RechargeRequest, error) {
	query := `SELECT id, user_id, amount, points_to_grant, order_id, status, 
			  payment_method, payment_proof, admin_note, processed_by, 
			  processed_at, created_at, updated_at 
			  FROM recharge_requests WHERE id = $1 FOR UPDATE`

	var req recharge.RechargeRequest
	err := r.db.conn.GetContext(ctx, &req, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrNotFound
		}
		return nil, errors.NewDatabaseError("GetRechargeRequestForUpdate", "获取充值申请失败", err)
	}

	return &req, nil
}

// GetRechargeRequestByOrderID 根据订单号获取充值申请
func (r *rechargeRepository) GetRechargeRequestByOrderID(ctx context.Context, orderID string) (*recharge.RechargeRequest, error) {
	query := `SELECT id, user_id, amount, points_to_grant, order_id, status, 
			  payment_method, payment_proof, admin_note, processed_by, 
			  processed_at, created_at, updated_at 
			  FROM recharge_requests WHERE order_id = $1`

	var req recharge.RechargeRequest
	err := r.db.conn.GetContext(ctx, &req, query, orderID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrNotFound
		}
		return nil, errors.NewDatabaseError("GetRechargeRequestByOrderID", "获取充值申请失败", err)
	}

	return &req, nil
}

// UpdateRechargeRequest 更新充值申请
func (r *rechargeRepository) UpdateRechargeRequest(ctx context.Context, req *recharge.RechargeRequest) error {
	query := `UPDATE recharge_requests 
			  SET status = $2, admin_note = $3, processed_by = $4, 
			      processed_at = $5, updated_at = NOW() 
			  WHERE id = $1`

	_, err := r.db.conn.ExecContext(ctx, query,
		req.ID, req.Status, req.AdminNote, req.ProcessedBy, req.ProcessedAt)
	if err != nil {
		return errors.NewDatabaseError("UpdateRechargeRequest", "更新充值申请失败", err)
	}

	return nil
}

// GetUserRechargeRequests 获取用户充值申请列表
func (r *rechargeRepository) GetUserRechargeRequests(ctx context.Context, userID uuid.UUID, limit, offset int) ([]recharge.RechargeRequest, int, error) {
	// 获取总数
	countQuery := `SELECT COUNT(*) FROM recharge_requests WHERE user_id = $1`
	var total int
	err := r.db.conn.GetContext(ctx, &total, countQuery, userID)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetUserRechargeRequests", "获取充值申请总数失败", err)
	}

	// 获取充值申请列表
	query := `SELECT id, user_id, amount, points_to_grant, order_id, status, 
			  payment_method, payment_proof, admin_note, processed_by, 
			  processed_at, created_at, updated_at 
			  FROM recharge_requests WHERE user_id = $1 
			  ORDER BY created_at DESC LIMIT $2 OFFSET $3`

	var requests []recharge.RechargeRequest
	err = r.db.conn.SelectContext(ctx, &requests, query, userID, limit, offset)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetUserRechargeRequests", "获取充值申请列表失败", err)
	}

	return requests, total, nil
}

// GetAdminRechargeRequests 获取管理员充值申请列表
func (r *rechargeRepository) GetAdminRechargeRequests(ctx context.Context, status recharge.ApprovalStatus, limit, offset int) ([]recharge.RechargeRequest, int, error) {
	var countQuery, query string
	var args []any

	if status != "" {
		countQuery = `SELECT COUNT(*) FROM recharge_requests WHERE status = $1`
		query = `SELECT id, user_id, amount, points_to_grant, order_id, status, 
				 payment_method, payment_proof, admin_note, processed_by, 
				 processed_at, created_at, updated_at 
				 FROM recharge_requests WHERE status = $1 
				 ORDER BY created_at DESC LIMIT $2 OFFSET $3`
		args = []any{status, limit, offset}
	} else {
		countQuery = `SELECT COUNT(*) FROM recharge_requests`
		query = `SELECT id, user_id, amount, points_to_grant, order_id, status, 
				 payment_method, payment_proof, admin_note, processed_by, 
				 processed_at, created_at, updated_at 
				 FROM recharge_requests 
				 ORDER BY created_at DESC LIMIT $1 OFFSET $2`
		args = []any{limit, offset}
	}

	// 获取总数
	var total int
	if status != "" {
		err := r.db.conn.GetContext(ctx, &total, countQuery, status)
		if err != nil {
			return nil, 0, errors.NewDatabaseError("GetAdminRechargeRequests", "获取充值申请总数失败", err)
		}
	} else {
		err := r.db.conn.GetContext(ctx, &total, countQuery)
		if err != nil {
			return nil, 0, errors.NewDatabaseError("GetAdminRechargeRequests", "获取充值申请总数失败", err)
		}
	}

	// 获取充值申请列表
	var requests []recharge.RechargeRequest
	err := r.db.conn.SelectContext(ctx, &requests, query, args...)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetAdminRechargeRequests", "获取充值申请列表失败", err)
	}

	return requests, total, nil
}

// GetAllRechargeRequests 获取所有充值申请
func (r *rechargeRepository) GetAllRechargeRequests(ctx context.Context, limit, offset int) ([]recharge.RechargeRequest, int, error) {
	return r.GetAdminRechargeRequests(ctx, "", limit, offset)
}

// GetRechargeStats 获取充值统计
func (r *rechargeRepository) GetRechargeStats(ctx context.Context, startDate, endDate string) (*interfaces.RechargeStats, error) {
	query := `
	SELECT 
		COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as total_amount,
		COUNT(*) as total_count,
		COALESCE(AVG(CASE WHEN status = 'approved' THEN amount ELSE NULL END), 0) as average_amount,
		COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
		COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
		COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count
	FROM recharge_requests 
	WHERE created_at >= $1::date AND created_at < $2::date + INTERVAL '1 day'`

	var stats interfaces.RechargeStats
	err := r.db.conn.GetContext(ctx, &stats, query, startDate, endDate)
	if err != nil {
		return nil, errors.NewDatabaseError("GetRechargeStats", "获取充值统计失败", err)
	}

	return &stats, nil
}
