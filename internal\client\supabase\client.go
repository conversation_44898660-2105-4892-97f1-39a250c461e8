package supabase

import (
	"serverless-aig/internal/config"

	"github.com/google/uuid"

	"github.com/supabase-community/auth-go"
	"github.com/supabase-community/auth-go/types"
)

// Client Supabase客户端
type Client struct {
	client auth.Client
}

// New 创建新的Supabase客户端
func New(cfg config.SupabaseConfig, serviceMode string) *Client {
	if cfg.URL == "" || cfg.Key == "" {
		panic("Supabase URL和Key不能为空")
	}

	// 初始化Supabase Auth客户端
	client := auth.New(cfg.ID, cfg.Key)
	client = client.WithCustomAuthURL(cfg.URL + "/auth/v1")

	// 管理员模式下使用服务密钥
	if serviceMode == "admin" {
		client = client.WithToken(cfg.Key)
	}

	return &Client{
		client: client,
	}
}

// Login 用户登录
func (c *Client) Login(email, password string) (*types.TokenResponse, error) {
	return c.client.SignInWithEmailPassword(email, password)
}

// Register 用户注册
func (c *Client) Register(email, password string) (*types.UserResponse, error) {
	signupResp, err := c.client.Signup(types.SignupRequest{
		Email:    email,
		Password: password,
	})
	if err != nil {
		return nil, err
	}
	return &types.UserResponse{User: signupResp.User}, nil
}

// RefreshToken 刷新令牌
func (c *Client) RefreshToken(refreshToken string) (*types.TokenResponse, error) {
	return c.client.RefreshToken(refreshToken)
}

// GetUser 获取用户信息
func (c *Client) GetUser(accessToken string) (*types.UserResponse, error) {
	// 使用访问令牌创建一个新的客户端实例
	clientWithToken := c.client.WithToken(accessToken)
	return clientWithToken.GetUser()
}

// SendPasswordResetEmail 发送密码重置邮件
func (c *Client) SendPasswordResetEmail(email string) error {
	return c.client.Recover(types.RecoverRequest{
		Email: email,
	})
}

// VerifyOTP 验证OTP
func (c *Client) VerifyOTP(email, token, verifyType string) (*types.TokenResponse, error) {
	resp, err := c.client.VerifyForUser(types.VerifyForUserRequest{
		Type:  types.VerificationType(verifyType),
		Token: token,
		Email: email,
	})
	if err != nil {
		return nil, err
	}
	return &types.TokenResponse{Session: resp.Session}, nil
}

// UpdatePassword 更新密码
func (c *Client) UpdatePassword(accessToken, newPassword string) (*types.UserResponse, error) {
	// 使用访问令牌创建一个新的客户端实例
	clientWithToken := c.client.WithToken(accessToken)
	updateResp, err := clientWithToken.UpdateUser(types.UpdateUserRequest{
		Password: &newPassword,
	})
	if err != nil {
		return nil, err
	}
	return &types.UserResponse{User: updateResp.User}, nil
}

// SendEmailOTP 发送邮箱OTP
func (c *Client) SendEmailOTP(email string) error {
	return c.client.OTP(types.OTPRequest{
		Email:      email,
		CreateUser: false, // 不自动创建用户，只发送 OTP
	})
}

// SignInWithOTP 使用OTP登录
func (c *Client) SignInWithOTP(email, token string) (*types.TokenResponse, error) {
	resp, err := c.client.VerifyForUser(types.VerifyForUserRequest{
		Type:  "email",
		Token: token,
		Email: email,
	})
	if err != nil {
		return nil, err
	}
	return &types.TokenResponse{Session: resp.Session}, nil
}

// AdminListUsers 管理员获取用户列表
func (c *Client) AdminListUsers(page, perPage int) (*types.AdminListUsersResponse, error) {
	return c.client.AdminListUsers(types.AdminListUsersRequest{
		Page:    &page,
		PerPage: &perPage,
	})
}

// AdminCreateUser 管理员创建用户
func (c *Client) AdminCreateUser(email, password string) (*types.UserResponse, error) {
	resp, err := c.client.AdminCreateUser(types.AdminCreateUserRequest{
		Email:        email,
		Password:     &password,
		EmailConfirm: true, // 自动确认邮箱
		UserMetadata: map[string]any{},
		AppMetadata:  map[string]any{},
	})
	if err != nil {
		return nil, err
	}
	return &types.UserResponse{User: resp.User}, nil
}

// AdminGetUser 管理员获取用户信息
func (c *Client) AdminGetUser(userID string) (*types.UserResponse, error) {
	// 将字符串转换为 UUID
	uid, err := uuid.Parse(userID)
	if err != nil {
		return nil, err
	}

	resp, err := c.client.AdminGetUser(types.AdminGetUserRequest{
		UserID: uid,
	})
	if err != nil {
		return nil, err
	}
	return &types.UserResponse{User: resp.User}, nil
}
