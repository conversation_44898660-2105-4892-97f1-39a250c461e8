package errors

import (
	"errors"
	"fmt"
)

// 预定义错误
var (
	ErrInsufficientBalance = errors.New("余额不足")
	ErrUserNotFound        = errors.New("用户不存在")
	ErrInvalidRequest      = errors.New("无效的请求")
	ErrUnauthorized        = errors.New("未授权")
	ErrForbidden           = errors.New("权限不足")
	ErrNotFound            = errors.New("资源不存在")
	ErrConflict            = errors.New("资源冲突")
	ErrInternalServer      = errors.New("内部服务器错误")
	ErrBadRequest          = errors.New("请求参数错误")
	ErrServiceUnavailable  = errors.New("服务不可用")
)

// BusinessError 业务错误
type BusinessError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *BusinessError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// NewBusinessError 创建业务错误
func NewBusinessError(code, message string) *BusinessError {
	return &BusinessError{
		Code:    code,
		Message: message,
	}
}

// NewBusinessErrorWithDetails 创建带详情的业务错误
func NewBusinessErrorWithDetails(code, message, details string) *BusinessError {
	return &BusinessError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation error on field '%s': %s", e.Field, e.Message)
}

// NewValidationError 创建验证错误
func NewValidationError(field, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
	}
}

// DatabaseError 数据库错误
type DatabaseError struct {
	Operation string `json:"operation"`
	Message   string `json:"message"`
	Err       error  `json:"-"`
}

func (e *DatabaseError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("database error in %s: %s (%v)", e.Operation, e.Message, e.Err)
	}
	return fmt.Sprintf("database error in %s: %s", e.Operation, e.Message)
}

func (e *DatabaseError) Unwrap() error {
	return e.Err
}

// NewDatabaseError 创建数据库错误
func NewDatabaseError(operation, message string, err error) *DatabaseError {
	return &DatabaseError{
		Operation: operation,
		Message:   message,
		Err:       err,
	}
}

// ExternalServiceError 外部服务错误
type ExternalServiceError struct {
	Service string `json:"service"`
	Message string `json:"message"`
	Err     error  `json:"-"`
}

func (e *ExternalServiceError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("external service error from %s: %s (%v)", e.Service, e.Message, e.Err)
	}
	return fmt.Sprintf("external service error from %s: %s", e.Service, e.Message)
}

func (e *ExternalServiceError) Unwrap() error {
	return e.Err
}

// NewExternalServiceError 创建外部服务错误
func NewExternalServiceError(service, message string, err error) *ExternalServiceError {
	return &ExternalServiceError{
		Service: service,
		Message: message,
		Err:     err,
	}
}
