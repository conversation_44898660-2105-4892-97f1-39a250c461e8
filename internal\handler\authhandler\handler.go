package authhandler

import (
	"serverless-aig/internal/domain/auth"
	"serverless-aig/internal/logger"
	authservice "serverless-aig/internal/service/auth"
	"serverless-aig/pkg/response"

	"github.com/gin-gonic/gin"
)

// Handler 认证处理器
type Handler struct {
	authService authservice.Service
	logger      *logger.Logger
}

// New 创建认证处理器
func New(authService authservice.Service, logger *logger.Logger) *Handler {
	return &Handler{
		authService: authService,
		logger:      logger,
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 使用邮箱和密码登录系统，成功后返回访问令牌和用户信息
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body auth.LoginRequest true "登录信息" example({"email":"<EMAIL>","password":"password123"})
// @Success 200 {object} auth.AuthResponse "登录成功，返回访问令牌和用户信息"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如邮箱格式不正确或密码长度不足"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "认证失败，邮箱或密码错误"
// @Router /api/v1/auth/login [post]
func (h *Handler) Login(c *gin.Context) {
	var req auth.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	resp, err := h.authService.Login(&req)
	if err != nil {
		h.logger.LogError("登录失败: %v", err)
		response.Unauthorized(c, err)
		return
	}

	response.Success(c, resp)
}

// Register 用户注册
// @Summary 用户注册
// @Description 使用邮箱和密码注册新用户，自动创建钱包并赠送500积分
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body auth.RegisterRequest true "注册信息" example({"email":"<EMAIL>","password":"password123"})
// @Success 200 {object} auth.AuthResponse "注册成功，返回访问令牌和用户信息"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如邮箱已存在或密码格式不正确"
// @Router /api/v1/auth/register [post]
func (h *Handler) Register(c *gin.Context) {
	var req auth.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	resp, err := h.authService.Register(&req)
	if err != nil {
		h.logger.LogError("注册失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, resp, "注册成功")
}

// RefreshToken 刷新令牌
// @Summary 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌，延长用户会话时间
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body auth.RefreshTokenRequest true "刷新令牌信息" example({"refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."})
// @Success 200 {object} auth.AuthResponse "刷新成功，返回新的访问令牌"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误或刷新令牌无效"
// @Router /api/v1/auth/refresh [post]
func (h *Handler) RefreshToken(c *gin.Context) {
	var req auth.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	resp, err := h.authService.RefreshToken(&req)
	if err != nil {
		h.logger.LogError("刷新令牌失败: %v", err)
		response.Unauthorized(c, err)
		return
	}

	response.Success(c, resp)
}

// Recover 密码恢复
// @Summary 发送密码重置邮件
// @Description 向用户邮箱发送密码重置邮件，用户需要从邮件中复制令牌进行密码重置
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body auth.RecoverRequest true "邮箱地址" example({"email":"<EMAIL>"})
// @Success 200 {object} response.Response "密码重置邮件发送成功"
// @Failure 400 {object} response.Response "请求参数错误，如邮箱格式不正确或用户不存在"
// @Router /api/v1/auth/recover [post]
func (h *Handler) Recover(c *gin.Context) {
	var req auth.RecoverRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	err := h.authService.Recover(&req)
	if err != nil {
		h.logger.LogError("密码恢复失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, nil, "密码重置邮件已发送")
}

// Verify 验证
// @Summary 验证令牌
// @Description 验证邮箱验证令牌或密码重置令牌，支持多种验证类型
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body auth.VerifyRequest true "验证信息" example({"email":"<EMAIL>","token":"123456","type":"email_verification"})
// @Success 200 {object} auth.AuthResponse "验证成功，返回用户信息"
// @Failure 400 {object} response.Response "请求参数错误或令牌无效/过期"
// @Router /api/v1/auth/verify [post]
func (h *Handler) Verify(c *gin.Context) {
	var req auth.VerifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	resp, err := h.authService.Verify(&req)
	if err != nil {
		h.logger.LogError("验证失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, resp, "验证成功")
}

// UpdatePassword 更新密码
// @Summary 更新密码
// @Description 使用密码重置令牌更新用户密码，无需登录状态
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body auth.UpdatePasswordRequest true "新密码信息" example({"password":"newpassword123"})
// @Success 200 {object} response.Response "密码更新成功"
// @Failure 400 {object} response.Response "请求参数错误，如密码格式不符合要求"
// @Failure 401 {object} response.Response "未授权或令牌无效"
// @Router /api/v1/auth/update-password [post]
func (h *Handler) UpdatePassword(c *gin.Context) {
	var req auth.UpdatePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	// 获取访问令牌
	accessToken := c.GetHeader("Authorization")
	if accessToken == "" {
		accessToken = c.GetHeader("X-Access-Token")
	}

	err := h.authService.UpdatePassword(accessToken, &req)
	if err != nil {
		h.logger.LogError("更新密码失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, nil, "密码更新成功")
}

// SendEmailCode 发送邮箱验证码
// @Summary 发送邮箱验证码
// @Description 向用户邮箱发送验证码
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body auth.EmailCodeRequest true "邮箱地址"
// @Success 200 {object} response.Response "验证码发送成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Router /api/v1/auth/email-code [post]
func (h *Handler) SendEmailCode(c *gin.Context) {
	var req auth.EmailCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	err := h.authService.SendEmailCode(&req)
	if err != nil {
		h.logger.LogError("发送邮箱验证码失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, nil, "验证码已发送到您的邮箱")
}

// EmailLogin 邮箱验证码登录
// @Summary 邮箱验证码登录
// @Description 使用邮箱验证码登录
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body auth.EmailLoginRequest true "邮箱和验证码"
// @Success 200 {object} auth.AuthResponse "登录成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Router /api/v1/auth/email-login [post]
func (h *Handler) EmailLogin(c *gin.Context) {
	var req auth.EmailLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	resp, err := h.authService.EmailLogin(&req)
	if err != nil {
		h.logger.LogError("邮箱验证码登录失败: %v", err)
		response.Unauthorized(c, err)
		return
	}

	response.Success(c, resp)
}

// AdminGetUserList 管理员获取用户列表
// @Summary 获取用户列表
// @Description 管理员获取系统中所有用户的列表
// @Tags 管理员用户
// @Security BearerAuth
// @Produce json
// @Param limit query int false "每页数量"
// @Param offset query int false "偏移量"
// @Success 200 {object} auth.UserListResponse "用户列表"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Router /api/v1/admin/user/list [get]
func (h *Handler) AdminGetUserList(c *gin.Context) {
	var req auth.UserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ValidationError(c, "query", err.Error())
		return
	}

	resp, err := h.authService.AdminGetUserList(&req)
	if err != nil {
		h.logger.LogError("获取用户列表失败: %v", err)
		response.InternalServerError(c, err)
		return
	}

	response.Success(c, resp)
}

// AdminCreateUser 管理员创建用户
// @Summary 创建用户
// @Description 管理员创建新用户
// @Tags 管理员用户
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body auth.CreateUserRequest true "用户信息"
// @Success 200 {object} auth.AuthResponse "用户创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Router /api/v1/admin/user/create [post]
func (h *Handler) AdminCreateUser(c *gin.Context) {
	var req auth.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	resp, err := h.authService.AdminCreateUser(&req)
	if err != nil {
		h.logger.LogError("创建用户失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, resp, "用户创建成功")
}

// AdminGetUserInfo 管理员获取用户信息
// @Summary 获取用户信息
// @Description 管理员获取指定用户的详细信息
// @Tags 管理员用户
// @Security BearerAuth
// @Produce json
// @Param user_id query string true "用户ID"
// @Success 200 {object} auth.User "用户信息"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Router /api/v1/admin/user/info [get]
func (h *Handler) AdminGetUserInfo(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		response.ValidationError(c, "user_id", "用户ID不能为空")
		return
	}

	user, err := h.authService.AdminGetUserInfo(userID)
	if err != nil {
		h.logger.LogError("获取用户信息失败: %v", err)
		response.NotFound(c, err)
		return
	}

	response.Success(c, user)
}
