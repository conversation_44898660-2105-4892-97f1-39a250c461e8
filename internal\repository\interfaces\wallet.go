package interfaces

import (
	"context"
	"serverless-aig/internal/domain/wallet"

	"github.com/google/uuid"
)

// WalletRepository 钱包仓储接口
type WalletRepository interface {
	// 钱包操作
	GetWallet(ctx context.Context, userID uuid.UUID) (*wallet.Wallet, error)
	CreateWallet(ctx context.Context, userID uuid.UUID) (*wallet.Wallet, error)
	UpdateWallet(ctx context.Context, w *wallet.Wallet) error

	// 交易操作
	CreateTransaction(ctx context.Context, tx *wallet.Transaction) error
	GetTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]wallet.Transaction, int, error)
	GetTransactionsBySource(ctx context.Context, userID uuid.UUID, source wallet.TransactionSource, limit, offset int) ([]wallet.Transaction, int, error)

	// 余额操作
	ConsumePoints(ctx context.Context, req *wallet.ConsumePointsRequest) (*wallet.Wallet, error)
	AdjustBalance(ctx context.Context, userID uuid.UUID, amount int64, description string) (*wallet.Wallet, error)
	AddPaidBalance(ctx context.Context, userID uuid.UUID, amount int64, description string, relatedRechargeID *int) (*wallet.Wallet, error)

	// 统计操作
	GetTotalBalance(ctx context.Context, userID uuid.UUID) (int64, error)
	GetUserCount(ctx context.Context) (int, error)
}
