package ai

// AnalysisMode 分析模式枚举
type AnalysisMode string

const (
	// TraditionalMode 传统模式：OCR + 分析
	TraditionalMode AnalysisMode = "traditional"
	// ThinkingMode 深度思考模式：直接多模态分析
	ThinkingMode AnalysisMode = "thinking"
	// StandardMode 智能均衡模式：直接多模态分析
	StandardMode AnalysisMode = "standard"
	// EconomyMode 经济极速模式：快速多模态分析
	EconomyMode AnalysisMode = "economy"
)

// ModelPricing 模型定价
type ModelPricing struct {
	PromptPrice     float64 `json:"prompt_price"`     // 输入价格（每token）
	CompletionPrice float64 `json:"completion_price"` // 输出价格（每token）
}

// AnalysisRequest 分析请求
type AnalysisRequest struct {
	Text         string       `json:"text" binding:"required"`       // 评分标准
	PromptKey    string       `json:"prompt_key" binding:"required"` // 系统提示词的键值
	Content      string       `json:"content" binding:"required"`    // 学生答案图片url
	AnalysisMode AnalysisMode `json:"analysis_mode,omitempty"`       // 分析模式
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	StudentAnswer  string `json:"student_answer"`  // 学生答案内容
	Score          int    `json:"score"`           // 计算得到的最终数字得分
	GradingDetails string `json:"grading_details"` // 详细说明得分点和扣分点
}

// AnalysisResponse 分析响应
type AnalysisResponse struct {
	Result     AnalysisResult `json:"result"`
	TokensUsed TokenUsage     `json:"tokens_used"`
	CostInfo   CostInfo       `json:"cost_info"`
	Balance    BalanceInfo    `json:"balance"`
	Mode       AnalysisMode   `json:"mode"`
	Models     ModelsUsed     `json:"models"`
}

// TokenUsage 令牌使用情况
type TokenUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// CostInfo 成本信息
type CostInfo struct {
	PromptCost     float64 `json:"prompt_cost"`
	CompletionCost float64 `json:"completion_cost"`
	TotalCost      float64 `json:"total_cost"`
	PointsConsumed int64   `json:"points_consumed"`
}

// BalanceInfo 余额信息
type BalanceInfo struct {
	PaidBalance  int64 `json:"paid_balance"`
	FreeBalance  int64 `json:"free_balance"`
	TotalBalance int64 `json:"total_balance"`
}

// ModelsUsed 使用的模型
type ModelsUsed struct {
	OCRModel      string `json:"ocr_model,omitempty"`
	AnalysisModel string `json:"analysis_model"`
}

// ConfigItem 配置项
type ConfigItem struct {
	ID      string `json:"id" db:"id"`
	Name    string `json:"name" db:"name"`
	URL     string `json:"url" db:"url"`
	Actions string `json:"actions" db:"actions"`
}

// Subject 学科
type Subject struct {
	Key         string `json:"key" db:"prompt_key"`
	Text        string `json:"text" db:"prompt_text"`
	Description string `json:"description" db:"description"`
	Category    string `json:"category" db:"category"`
}

// 模型映射
var ModelMap = map[string]int{
	"deepseek-v3-250324":               1,
	"deepseek-r1-250120":               2,
	"doubao-1-5-vision-pro-32k-250115": 3,
	"doubao-1.5-vision-pro-250328":     4,
	"doubao-seed-1-6-250615":           5,
	"doubao-seed-1-6-flash-250615":     6,
	"doubao-seed-1-6-thinking-250715":  7,
}

// 默认成本倍率
const DefaultCostMultiplier = 2

// 最大令牌数
const MaxTokens = 32000

// 默认价格
var DefaultPricing = ModelPricing{
	PromptPrice:     0.003 / 1000,
	CompletionPrice: 0.009 / 1000,
}

// GetModeModels 获取模式对应的模型
func GetModeModels(mode AnalysisMode) (ocrModel, analysisModel string) {
	switch mode {
	case TraditionalMode:
		return "doubao-1-5-vision-pro-32k-250115", "deepseek-v3-250324"
	case ThinkingMode:
		return "", "doubao-seed-1-6-thinking-250715"
	case StandardMode:
		return "", "doubao-seed-1-6-250615"
	case EconomyMode:
		return "", "doubao-seed-1-6-flash-250615"
	default:
		return "", "doubao-seed-1-6-250615"
	}
}

// IsValidMode 检查分析模式是否有效
func IsValidMode(mode AnalysisMode) bool {
	switch mode {
	case TraditionalMode, ThinkingMode, StandardMode, EconomyMode:
		return true
	default:
		return false
	}
}
