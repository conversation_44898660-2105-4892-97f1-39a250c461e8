package interfaces

import (
	"context"
	"serverless-aig/internal/domain/finance"
)

// FinanceRepository 财务仓储接口
type FinanceRepository interface {
	// 支出操作
	CreateExpense(ctx context.Context, expense *finance.Expense) error
	GetExpense(ctx context.Context, id int64) (*finance.Expense, error)
	UpdateExpense(ctx context.Context, expense *finance.Expense) error
	DeleteExpense(ctx context.Context, id int64) error

	// 查询操作
	GetExpenses(ctx context.Context, req *finance.ExpenseListRequest) ([]finance.Expense, int, error)
	GetExpensesByCategory(ctx context.Context, category finance.ExpenseCategory, startDate, endDate string) ([]finance.Expense, error)
	GetExpensesByDateRange(ctx context.Context, startDate, endDate string) ([]finance.Expense, error)

	// 统计操作
	GetFinancialReport(ctx context.Context, startDate, endDate string) (*finance.FinancialReport, error)
	GetExpenseAnalysis(ctx context.Context, startDate, endDate string) (*finance.ExpenseAnalysis, error)
	GetTotalExpenses(ctx context.Context, startDate, endDate string) (int64, error)
	GetExpensesByVendor(ctx context.Context, startDate, endDate string, limit int) ([]finance.VendorExpense, error)
	GetMonthlyExpenses(ctx context.Context, startDate, endDate string) ([]finance.MonthlyExpense, error)
}

// AIRepository AI相关仓储接口
type AIRepository interface {
	// 配置操作
	GetConfigs(ctx context.Context) ([]ConfigItem, error)
	GetConfig(ctx context.Context, id string) (*ConfigItem, error)

	// 提示词操作
	GetPrompts(ctx context.Context) ([]PromptItem, error)
	GetPrompt(ctx context.Context, key string) (*PromptItem, error)

	// 科目操作
	GetSubjects(ctx context.Context) ([]SubjectItem, error)
}

// ConfigItem 配置项
type ConfigItem struct {
	ID      string `json:"id" db:"id"`
	Name    string `json:"name" db:"name"`
	URL     string `json:"url" db:"url"`
	Actions string `json:"actions" db:"actions"`
}

// PromptItem 提示词项
type PromptItem struct {
	Key         string `json:"key" db:"prompt_key"`
	Text        string `json:"text" db:"prompt_text"`
	Description string `json:"description" db:"description"`
	Category    string `json:"category" db:"category"`
}

// SubjectItem 科目项（只包含 key 和 description）
type SubjectItem struct {
	Key         string `json:"key" db:"prompt_key"`
	Description string `json:"description" db:"description"`
}
