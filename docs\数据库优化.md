# 数据库优化 (PostgreSQL)

构建一个健壮财务系统，需要优化数据库设计。优化的目的是提高系统的可靠性、可扩展性和可维护性。优化的核心思想是将钱包中的积分明确分为“付费积分”和“赠送积分”，并在每一笔交易中都清晰地记录这两种积分的变化。这使得每一笔账都有据可查，审计性极强。

## 优化后的数据库设计

优化后的表结构 sql/postgresql_table_new.sql。

设计变更的理由：

1. wallets 表:
    - balance 拆分为 paid_balance 和 free_balance。这是实现收入核算的基础。你可以随时知道欠用户的“预付款”是多少 (paid_balance)，以及送出了多少“营销成本” (free_balance)。

2. transactions 表:
    - 单一 amount 拆分为 paid_points_change 和 free_points_change: 这是最重要的改变。每一笔交易都明确记录了对付费池 和免费池的影响。例如，一笔消耗 1200 积分的交易，如果用户当时有 800 赠送积分，则记录为 free_points_change: -800, paid_points_change: -400。
    - 增加了\_after 字段: paid_balance_after 和 free_balance_after 提供了交易后的余额快照。这极大地增强了 审计能力。你可以轻易地验证上一条交易的 balance_after + 当前交易的 change 是否等于 当前交易的 balance_after。
    - 更明确的 source: transaction_source 枚举类型让交易目的更加清晰。
    - 关联 ID: related_recharge_id 和 related_consumption_id 将财务流水与业务操作关联 起来，便于排查问题。

## 核心业务逻辑实现

重要提示： 所有涉及资金和积分变动的操作，使用 Writable CTE 来实现，保证操作的原子性和高效性。

### 新用户注册

1. 在 wallets 表中为新用户创建一条记录，paid_balance 为0, free_balance 为500。
2. 在 transactions 表中插入一条记录：
    - user_id: 新用户ID
    - source: 'GIFT_SIGNUP'
    - paid_points_change: 0
    - free_points_change: 500
    - paid_balance_after: 0
    - free_balance_after: 500

### 管理员审批充值

1. 管理员批准 `recharge_requests` 中的一条请求。
2. 获取用户的 `wallets` 记录并**加锁** (`SELECT ... FOR UPDATE`) 防止并发问题。
3. 获取要增加的付费积分`recharge_requests.points_to_grant`
4. 更新 `wallets` 表：`UPDATE wallets SET paid_balance = paid_balance + points_to_grant, updated_at = NOW() WHERE user_id = ...`。
5. 获取更新后的 `paid_balance` 和 `free_balance`。
6. 在 `transactions` 表中插入一条记录：
    - `user_id`: 用户ID
    - `source`: `'RECHARGE'`
    - `paid_points_change`: `points_to_grant`
    - `free_points_change`: 0
    - `paid_balance_after`: 更新后的 `paid_balance`
    - `free_balance_after`: 当前的 `free_balance`
    - `description`: `充值${amount}元，获得${points_to_grant}积分`
    - `related_recharge_id`: 被批准的 `recharge_requests.id`

### 用户消费积分（核心规则）

这里我们采用对公司最有利、也最常见的规则：**优先消耗赠送积分 (Free First)**。

1. 获取需要消耗的总积分 `total_consumption`。
2. 获取用户的 `wallets` 记录并**加锁** (`SELECT ... FOR UPDATE`)。
3. 检查总余额是否足够：`if (wallet.paid_balance + wallet.free_balance) < total_consumption`，则抛出余额不足异常。
4. 计算本次消耗分别从哪个池子扣除：

    ```javascript
    let points_from_free = Math.min(total_consumption, wallet.free_balance);
    let remaining_consumption = total_consumption - points_from_free;
    let points_from_paid = Math.min(remaining_consumption, wallet.paid_balance);
    ```

5. 更新 `wallets` 表：`UPDATE wallets SET free_balance = free_balance - points_from_free, paid_balance = paid_balance - points_from_paid, updated_at = NOW() WHERE user_id = ...`。
6. 获取更新后的 `paid_balance` 和 `free_balance`。
7. 在 `transactions` 表中插入一条记录：
    - `user_id`: 用户ID
    - `source`: `'CONSUMPTION'`
    - `paid_points_change`: `-points_from_paid` (注意是负数)
    - `free_points_change`: `-points_from_free` (注意是负数)
    - `paid_balance_after`: 更新后的 `paid_balance`
    - `free_balance_after`: 更新后的 `free_balance`
    - `metadata`: 按原实现
