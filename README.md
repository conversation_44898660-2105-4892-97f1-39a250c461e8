# Serverless-AIG

这是一个使用 Go 语言实现的 Serverless 后端服务，部署在腾讯云函数上。它提供了使用 Supabase 进行身份验证的功能。

## 功能

- 使用 Supabase 进行用户身份验证
- 提供受保护的 API 端点
- 支持火山引擎的图文理解 API
- 支持邮箱密码注册和登录
- 支持邮箱验证码登录
- 支持密码重置
- 支持钱包充值和余额查询
- 支持交易记录查询
- 支持火山引擎的AI模型API接口
- 支持图像分析API接口
- 支持腾讯云对象存储临时凭证获取
- 管理员可处理充值申请
- 管理员可调整用户余额
- 管理员可查询交易记录
- 管理员可查询统计信息
- 管理员可创建用户账号

## 配置

### Supabase 配置

设置以下环境变量来配置 Supabase：

```env
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
```

### AI 模型 API 配置

设置以下环境变量来配置 AI 模型 API：

```env
# 字节跳动模型配置（火山引擎）
ARK_API_KEY=your_ark_api_key


```

### Turso 数据库配置

设置以下环境变量来配置 Turso 数据库访问：

```env
# Turso 数据库信息
TURSO_DATABASE_URL=libsql://your-database-name.turso.io
TURSO_AUTH_TOKEN=your_turso_auth_token
```

这些环境变量是必需的，用于连接 Turso 数据库。

要获取 Turso 数据库 URL 和认证令牌，请按照以下步骤操作：

1. 注册并登录 [Turso](https://turso.tech/)
2. 安装 Turso CLI: `curl -sSfL https://get.tur.so/install.sh | bash`
3. 登录 CLI: `turso auth login`
4. 创建数据库: `turso db create your-database-name`
5. 获取数据库 URL: `turso db show your-database-name --url`
6. 创建认证令牌: `turso db tokens create your-database-name`

## API 端点

### 公共端点

- `GET /` - 服务器状态
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新访问令牌

### 受保护端点

- `GET /api/v1/protected` - 受保护的 API 端点，需要身份验证

### 钱包相关端点

- `GET /api/v1/wallet/balance` - 查询钱包余额，需要身份验证
- `POST /api/v1/wallet/deposit` - 充值到钱包，需要身份验证
- `GET /api/v1/wallet/transactions` - 查询交易记录，需要身份验证

### TOS 相关端点

- `GET /api/v1/tos/credentials` - 获取访问内网 TOS 的临时访问凭证，需要身份验证

### 系统提示词相关端点

- `GET /api/v1/prompts` - 获取所有系统提示词，需要身份验证
- `GET /api/v1/prompts/category?category=<分类名称>` - 获取特定分类的系统提示词，需要身份验证
- `POST /api/v1/prompts/upsert` - 创建或更新系统提示词，需要身份验证
- `DELETE /api/v1/prompts/delete?prompt_key=<提示词键>` - 删除系统提示词，需要身份验证

## 身份验证

要访问受保护的端点，您需要在请求头中包含一个有效的访问令牌，格式为：

`X-Access-Token: YOUR_ACCESS_TOKEN`

### 刷新令牌

当访问令牌过期时，可以使用刷新令牌获取新的访问令牌：

```bash
curl -X POST http://localhost:9000/api/v1/auth/refresh \
  -H "Content-Type: application/json" \
  -H "X-Refresh-Token: YOUR_REFRESH_TOKEN" \
  -d '{}'
```

或者在请求体中提供刷新令牌：

```bash
curl -X POST http://localhost:9000/api/v1/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "YOUR_REFRESH_TOKEN"
  }'
```

响应示例：

```json
{
  "access_token": "NEW_ACCESS_TOKEN",
  "refresh_token": "NEW_REFRESH_TOKEN",
  "expires_in": 3600,
  "expires_at": 1625097700,
  "config": [...],
  "subjects": [...]
}
```

注意：响应头中也会包含 `X-Access-Token` 和 `X-Refresh-Token` 头，客户端可以直接从响应头中提取这些令牌。

## API 使用示例

### AI API 使用示例

以下是使用 AI API 路由的示例：

```bash
curl -X POST http://localhost:9000/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN" \
  -d '{
    "content": "这张图片中有什么？",
    "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/...",
    "model": "doubao-1.5-vision-pro-250328",
    "subject": "general"
  }'
```

### 系统提示词 API 使用示例

#### 1. 获取所有系统提示词

```bash
curl -X GET http://localhost:9000/api/v1/prompts \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN"
```

响应示例：

```json
{
  "prompts": [
    {
      "prompt_key": "general",
      "prompt_text": "你是一个AI助手，请回答用户的问题。",
      "description": "通用提示词",
      "category": "general",
      "version": 1,
      "is_active": true,
      "created_at": 1625097600,
      "updated_at": 1625097600
    },
    {
      "prompt_key": "math",
      "prompt_text": "你是一个数学专家，请解答用户的数学问题。",
      "description": "数学提示词",
      "category": "education",
      "version": 2,
      "is_active": true,
      "created_at": 1625097700,
      "updated_at": 1625097800
    }
  ],
  "user_id": "user123"
}
```

#### 2. 获取特定分类的系统提示词

```bash
curl -X GET "http://localhost:9000/api/v1/prompts/category?category=education" \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN"
```

响应示例：

```json
{
  "prompts": [
    {
      "prompt_key": "math",
      "prompt_text": "你是一个数学专家，请解答用户的数学问题。",
      "description": "数学提示词",
      "category": "education",
      "version": 2,
      "is_active": true,
      "created_at": 1625097700,
      "updated_at": 1625097800
    },
    {
      "prompt_key": "physics",
      "prompt_text": "你是一个物理学专家，请解答用户的物理问题。",
      "description": "物理提示词",
      "category": "education",
      "version": 1,
      "is_active": true,
      "created_at": 1625097900,
      "updated_at": 1625097900
    }
  ],
  "category": "education"
}
```

#### 3. 创建或更新系统提示词

```bash
curl -X POST http://localhost:9000/api/v1/prompts/upsert \
  -H "Content-Type: application/json" \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN" \
  -d '{
    "prompt_key": "chemistry",
    "prompt_text": "你是一个化学专家，请解答用户的化学问题。",
    "description": "化学提示词",
    "category": "education",
    "is_active": true
  }'
```

响应示例：

```json
{
  "success": true,
  "prompt_key": "chemistry",
  "user_id": "user123",
  "message": "Prompt saved successfully"
}
```

#### 4. 删除系统提示词

```bash
curl -X DELETE "http://localhost:9000/api/v1/prompts/delete?prompt_key=chemistry" \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN"
```

响应示例：

```json
{
  "success": true,
  "prompt_key": "chemistry",
  "user_id": "user123",
  "rows_affected": 1,
  "message": "Prompt deleted successfully"
}
```

响应示例：

```json
{
  "id": "chatcmpl-123456789",
  "content": "这张图片显示了一个美丽的自然风景...",
  "usage": {
    "prompt_tokens": 100,
    "completion_tokens": 150,
    "total_tokens": 250
  },
  "cost": 0.00045
}
```

### 钱包 API 使用示例

#### 1. 查询钱包余额

```bash
curl -X GET http://localhost:9000/api/v1/wallet/balance \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN"
```

响应示例：

```json
{
  "balance": 100.5
}
```

#### 2. 充值到钱包

```bash
curl -X POST http://localhost:9000/api/v1/wallet/deposit \
  -H "Content-Type: application/json" \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN" \
  -d '{
    "amount": 50.0,
    "description": "充值50元",
    "metadata": {
      "payment_method": "alipay",
      "order_id": "12345"
    }
  }'
```

响应示例：

```json
{
  "success": true,
  "balance": 150.5,
  "amount": 50.0
}
```

#### 3. 查询交易记录

```bash
curl -X GET "http://localhost:9000/api/v1/wallet/transactions?limit=20&offset=0" \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN"
```

响应示例：

```json
{
  "transactions": [
    {
      "id": 2,
      "user_id": "user123",
      "type": "deposit",
      "amount": 50.0,
      "balance_after": 150.5,
      "description": "充值50元",
      "metadata": {
        "payment_method": "alipay",
        "order_id": "12345"
      },
      "created_at": 1625097700
    },
    {
      "id": 1,
      "user_id": "user123",
      "type": "consumption",
      "amount": 10.0,
      "balance_after": 100.5,
      "description": "使用doubao-1.5-vision-pro-250328模型",
      "metadata": {
        "model": "doubao-1.5-vision-pro-250328",
        "prompt_tokens": 100,
        "completion_tokens": 200,
        "total_tokens": 300
      },
      "created_at": 1625097600
    }
  ]
}
```

### TOS 临时凭证 API 使用示例

#### 获取 TOS 临时访问凭证

```bash
curl -X GET http://localhost:9000/api/v1/tos/credentials \
  -H "X-Access-Token: YOUR_ACCESS_TOKEN"
```

响应示例：

```json
{
  "success": true,
  "credentials": {
    "access_key_id": "STS.NTxxxxxxxxxxxxxxxx",
    "secret_access_key": "AhBxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "session_token": "IQoJb3JpZ2luX2VjEIz...(省略部分内容)...ZXhhbXBsZS9rZXktMy9hYWFhIl19",
    "expiration": "2023-12-31T12:00:00Z"
  },
  "bucket": "your-bucket-name",
  "endpoint": "tos-cn-beijing.volces.com",
  "region": "cn-beijing"
}
```

## 本地运行

```bash
go run .
```

服务器将在`0.0.0.0:9000`上启动。

## 部署到腾讯云函数

### 编译打包

Golang 编译可以在任意平台上通过指定 OS 及 ARCH 完成跨平台的编译，因此在 Linux，Windows 或 MacOS 下都可以进行编译。

- 在 Linux 或 MacOS 的终端通过如下方法完成编译及打包：文件编译：

`GOOS=linux GOARCH=amd64 go build -o main cmd/server/main.go`

- 在 Windows 下，请按照以下步骤进行编译打包：

按 Windows + R 打开运行窗口，输入 cmd 后按 Enter，打开命令行窗口。在命令行窗口中执行以下命令，设置编译参数：

```bash
set GOOS=linux
set GOARCH=amd64
```

这里将 GOOS 设置为 linux，GOARCH 设置为 amd64，表示编译生成的二进制文件适用于 Linux 系统的 64 位架构。在命令行窗口中执行以下命令，
进行编译：

`go build -o main cmd/server/main.go`

使用腾讯云 CLI 或控制台部署

对于 Web 函数，需将编译好的可执行二进制文件和 scf_bootstrap 文件一起压缩为一个 ZIP 文件。

命令可参考 `zip main.zip main scf_bootstrap`

## 腾讯云函数部署说明

本项目可以部署为两个不同的腾讯云函数，分别用于用户服务和管理员服务。这种分离允许使用不同的密钥和环境配置，提高安全性和可维护性。

### 部署结构

1. **用户服务 (serverless-aig-user)**

   - 包含公共路由
   - 包含钱包相关路由
   - 包含 OpenAPI 路由（AI 聊天和 OCR 分析）
   - 不包含管理员功能

2. **管理员服务 (serverless-aig-admin)**
   - 包含公共路由
   - 包含钱包相关路由
   - 包含充值申请相关路由
   - 包含管理员相关路由
   - 不包含 OpenAPI 路由

### 部署方法

部署两个服务可以使用腾讯云的 Serverless Framework：

```bash
# 安装Serverless Framework
npm install -g serverless

# 部署所有服务
serverless deploy

# 仅部署用户服务
serverless deploy --target user-service

# 仅部署管理员服务
serverless deploy --target admin-service
```

### 环境变量配置

两个服务可以使用不同的环境变量配置，特别是不同的 API 密钥：

1. **用户服务环境变量**

   - `FUNCTION_TYPE=user`（由 serverless.yml 自动设置）
   - 设置适合用户访问的 API 密钥和数据库凭证

2. **管理员服务环境变量**
   - `FUNCTION_TYPE=admin`（由 serverless.yml 自动设置）
   - 设置管理员专用的 API 密钥和数据库凭证
   - 设置`ADMIN_IDS`以指定管理员用户 ID 列表

可以在腾讯云 SCF 控制台为每个函数单独配置环境变量，或者在 serverless.yml 中进行配置。

### 本地测试

可以在本地使用不同的启动参数测试不同的服务模式：

```bash
# 测试用户服务模式
go run . -user

# 测试管理员服务模式
go run . -admin

# 使用.env文件中的环境变量
go run . -e
```

如果使用`-e`参数并且.env 文件中设置了`FUNCTION_TYPE`环境变量，程序会根据该环境变量自动选择服务模式。
