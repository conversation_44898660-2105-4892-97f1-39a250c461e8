basePath: /
definitions:
  ai.AnalysisMode:
    enum:
    - traditional
    - thinking
    - standard
    - economy
    type: string
    x-enum-varnames:
    - TraditionalMode
    - ThinkingMode
    - StandardMode
    - EconomyMode
  ai.AnalysisRequest:
    properties:
      analysis_mode:
        allOf:
        - $ref: '#/definitions/ai.AnalysisMode'
        description: 分析模式
      content:
        description: 学生答案图片url
        type: string
      prompt_key:
        description: 系统提示词的键值
        type: string
      text:
        description: 评分标准
        type: string
    required:
    - content
    - prompt_key
    - text
    type: object
  ai.AnalysisResponse:
    properties:
      balance:
        $ref: '#/definitions/ai.BalanceInfo'
      cost_info:
        $ref: '#/definitions/ai.CostInfo'
      mode:
        $ref: '#/definitions/ai.AnalysisMode'
      models:
        $ref: '#/definitions/ai.ModelsUsed'
      result:
        $ref: '#/definitions/ai.AnalysisResult'
      tokens_used:
        $ref: '#/definitions/ai.TokenUsage'
    type: object
  ai.AnalysisResult:
    properties:
      grading_details:
        description: 详细说明得分点和扣分点
        type: string
      score:
        description: 计算得到的最终数字得分
        type: integer
      student_answer:
        description: 学生答案内容
        type: string
    type: object
  ai.BalanceInfo:
    properties:
      free_balance:
        type: integer
      paid_balance:
        type: integer
      total_balance:
        type: integer
    type: object
  ai.ConfigItem:
    properties:
      actions:
        type: string
      id:
        type: string
      name:
        type: string
      url:
        type: string
    type: object
  ai.CostInfo:
    properties:
      completion_cost:
        type: number
      points_consumed:
        type: integer
      prompt_cost:
        type: number
      total_cost:
        type: number
    type: object
  ai.ModelsUsed:
    properties:
      analysis_model:
        type: string
      ocr_model:
        type: string
    type: object
  ai.Subject:
    properties:
      category:
        type: string
      description:
        type: string
      key:
        type: string
      text:
        type: string
    type: object
  ai.TokenUsage:
    properties:
      completion_tokens:
        type: integer
      prompt_tokens:
        type: integer
      total_tokens:
        type: integer
    type: object
  auth.AuthResponse:
    properties:
      access_token:
        type: string
      expires_at:
        type: integer
      expires_in:
        type: integer
      refresh_token:
        type: string
      subjects:
        description: 支持的科目列表
        items:
          $ref: '#/definitions/auth.Subject'
        type: array
      user:
        $ref: '#/definitions/auth.User'
    type: object
  auth.CreateUserRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  auth.EmailCodeRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  auth.EmailLoginRequest:
    properties:
      code:
        type: string
      email:
        type: string
    required:
    - code
    - email
    type: object
  auth.LoginRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  auth.RecoverRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  auth.RefreshTokenRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  auth.RegisterRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  auth.Subject:
    properties:
      description:
        description: description
        type: string
      key:
        description: prompt_key
        type: string
    type: object
  auth.UpdatePasswordRequest:
    properties:
      password:
        minLength: 6
        type: string
    required:
    - password
    type: object
  auth.User:
    properties:
      created_at:
        type: string
      email:
        type: string
      id:
        type: string
      updated_at:
        type: string
    type: object
  auth.UserListResponse:
    properties:
      count:
        type: integer
      success:
        type: boolean
      users:
        items:
          $ref: '#/definitions/auth.User'
        type: array
    type: object
  auth.VerifyRequest:
    properties:
      email:
        type: string
      token:
        type: string
      type:
        type: string
    required:
    - email
    - token
    - type
    type: object
  config.AppConfig:
    properties:
      admin_ids:
        items:
          type: string
        type: array
      download_url:
        type: string
      force_update:
        type: boolean
      update_log:
        type: string
      version:
        type: string
    type: object
  finance.AddExpenseRequest:
    properties:
      amount:
        description: 支付金额，单位：分
        minimum: 1
        type: integer
      category:
        description: 支出类别
        type: string
      description:
        description: 支出描述
        type: string
      invoice_url:
        description: 发票URL
        type: string
      payment_method:
        description: 支付方式
        type: string
      service_period_end:
        description: 服务周期结束，格式：YYYY-MM-DD
        type: string
      service_period_start:
        description: 服务周期开始，格式：YYYY-MM-DD
        type: string
      transaction_date:
        description: 实际支付日期，格式：YYYY-MM-DD
        type: string
      vendor:
        description: 供应商
        type: string
    required:
    - amount
    - category
    - description
    - service_period_end
    - service_period_start
    - transaction_date
    - vendor
    type: object
  finance.CategoryAnalysis:
    properties:
      amount:
        description: 金额（分）
        type: integer
      count:
        description: 记录数
        type: integer
      percentage:
        description: 占比
        type: number
    type: object
  finance.Expense:
    properties:
      amount:
        description: 支付金额，单位：分
        type: integer
      category:
        allOf:
        - $ref: '#/definitions/finance.ExpenseCategory'
        description: 支出类别
      created_at:
        type: string
      currency:
        description: 货币种类
        type: string
      description:
        type: string
      id:
        type: integer
      invoice_url:
        description: 发票URL
        type: string
      payment_method:
        description: 支付方式
        type: string
      recorded_by:
        description: 记录人
        type: string
      service_period_end:
        description: 服务周期结束
        type: string
      service_period_start:
        description: 服务周期开始
        type: string
      transaction_date:
        description: 实际支付日期
        type: string
      vendor:
        description: 供应商
        type: string
    type: object
  finance.ExpenseAnalysis:
    properties:
      category_breakdown:
        additionalProperties:
          $ref: '#/definitions/finance.CategoryAnalysis'
        type: object
      monthly_trend:
        items:
          $ref: '#/definitions/finance.MonthlyExpense'
        type: array
      top_vendors:
        items:
          $ref: '#/definitions/finance.VendorExpense'
        type: array
    type: object
  finance.ExpenseCategory:
    enum:
    - infrastructure
    - software
    - marketing
    - operational
    - other
    type: string
    x-enum-comments:
      CategoryInfrastructure: 基础设施
      CategoryMarketing: 市场营销
      CategoryOperational: 运营费用
      CategoryOther: 其他
      CategorySoftware: 软件服务
    x-enum-descriptions:
    - 基础设施
    - 软件服务
    - 市场营销
    - 运营费用
    - 其他
    x-enum-varnames:
    - CategoryInfrastructure
    - CategorySoftware
    - CategoryMarketing
    - CategoryOperational
    - CategoryOther
  finance.ExpenseListResponse:
    properties:
      count:
        type: integer
      expenses:
        items:
          $ref: '#/definitions/finance.Expense'
        type: array
      total:
        type: integer
    type: object
  finance.ExpenseReport:
    properties:
      expense_count:
        description: 支出记录数
        type: integer
      expenses_by_category:
        additionalProperties:
          format: int64
          type: integer
        description: 按类别分组的支出
        type: object
      total_expenses:
        description: 总支出（分）
        type: integer
    type: object
  finance.FinancialReport:
    properties:
      expenses:
        $ref: '#/definitions/finance.ExpenseReport'
      period:
        $ref: '#/definitions/finance.ReportPeriod'
      profit:
        $ref: '#/definitions/finance.ProfitReport'
      revenue:
        $ref: '#/definitions/finance.RevenueReport'
      user_metrics:
        $ref: '#/definitions/finance.UserMetricsReport'
    type: object
  finance.FinancialReportRequest:
    properties:
      end_date:
        description: '报表结束日期, 格式: YYYY-MM-DD'
        type: string
      start_date:
        description: '报表开始日期, 格式: YYYY-MM-DD'
        type: string
    required:
    - end_date
    - start_date
    type: object
  finance.MonthlyExpense:
    properties:
      amount:
        description: 金额（分）
        type: integer
      month:
        description: 月份，格式：YYYY-MM
        type: string
    type: object
  finance.ProfitReport:
    properties:
      gross_profit:
        description: 毛利润（分）
        type: integer
      profit_margin:
        description: 利润率
        type: number
    type: object
  finance.ReportPeriod:
    properties:
      end_date:
        type: string
      start_date:
        type: string
    type: object
  finance.RevenueReport:
    properties:
      average_recharge:
        description: 平均充值金额（分）
        type: integer
      recharge_count:
        description: 充值次数
        type: integer
      total_revenue:
        description: 总收入（分）
        type: integer
    type: object
  finance.UserMetricsReport:
    properties:
      active_users:
        description: 活跃用户数
        type: integer
      new_users:
        description: 新用户数
        type: integer
      paying_users:
        description: 付费用户数
        type: integer
    type: object
  finance.VendorExpense:
    properties:
      amount:
        description: 金额（分）
        type: integer
      vendor:
        description: 供应商名称
        type: string
    type: object
  models.SwaggerEmptyResponse:
    description: 无数据响应格式，通常用于删除或更新操作
    properties:
      message:
        description: 成功消息
        example: 操作成功
        type: string
      success:
        description: 请求成功标识
        example: true
        type: boolean
    type: object
  models.SwaggerForbiddenErrorResponse:
    description: 权限不足错误响应格式
    properties:
      code:
        description: 错误代码
        example: FORBIDDEN
        type: string
      error:
        description: 错误信息
        example: 需要管理员权限
        type: string
      message:
        description: 错误描述
        example: 权限不足
        type: string
      success:
        description: 请求失败标识
        example: false
        type: boolean
    type: object
  models.SwaggerInsufficientBalanceErrorResponse:
    description: 余额不足错误响应格式
    properties:
      code:
        description: 错误代码
        example: INSUFFICIENT_BALANCE
        type: string
      error:
        description: 错误信息
        example: 当前余额不足以完成此操作
        type: string
      message:
        description: 错误描述
        example: 余额不足
        type: string
      success:
        description: 请求失败标识
        example: false
        type: boolean
    type: object
  models.SwaggerInternalServerErrorResponse:
    description: 服务器内部错误响应格式
    properties:
      code:
        description: 错误代码
        example: INTERNAL_ERROR
        type: string
      error:
        description: 错误信息
        example: 数据库连接失败
        type: string
      message:
        description: 错误描述
        example: 服务器内部错误
        type: string
      success:
        description: 请求失败标识
        example: false
        type: boolean
    type: object
  models.SwaggerNotFoundErrorResponse:
    description: 资源不存在错误响应格式
    properties:
      code:
        description: 错误代码
        example: NOT_FOUND
        type: string
      error:
        description: 错误信息
        example: 用户不存在
        type: string
      message:
        description: 错误描述
        example: 资源不存在
        type: string
      success:
        description: 请求失败标识
        example: false
        type: boolean
    type: object
  models.SwaggerServiceUnavailableErrorResponse:
    description: 服务不可用错误响应格式
    properties:
      code:
        description: 错误代码
        example: SERVICE_UNAVAILABLE
        type: string
      error:
        description: 错误信息
        example: AI服务暂时不可用
        type: string
      message:
        description: 错误描述
        example: 外部服务不可用
        type: string
      success:
        description: 请求失败标识
        example: false
        type: boolean
    type: object
  models.SwaggerUnauthorizedErrorResponse:
    description: 未授权错误响应格式
    properties:
      code:
        description: 错误代码
        example: UNAUTHORIZED
        type: string
      error:
        description: 错误信息
        example: 访问令牌无效或已过期
        type: string
      message:
        description: 错误描述
        example: 未授权访问
        type: string
      success:
        description: 请求失败标识
        example: false
        type: boolean
    type: object
  models.SwaggerValidationErrorResponse:
    description: 参数验证错误响应格式
    properties:
      code:
        description: 错误代码
        example: VALIDATION_ERROR
        type: string
      error:
        description: 具体验证错误信息
        example: email字段格式不正确
        type: string
      message:
        description: 错误描述
        example: 请求参数验证失败
        type: string
      success:
        description: 请求失败标识
        example: false
        type: boolean
    type: object
  recharge.AdminProcessRequest:
    properties:
      action:
        allOf:
        - $ref: '#/definitions/recharge.ApprovalStatus'
        enum:
        - approved
        - rejected
      admin_note:
        type: string
      request_id:
        type: integer
      rmb_amount:
        description: 管理员可修改的实际充值金额
        type: number
    required:
    - action
    - request_id
    type: object
  recharge.ApprovalStatus:
    enum:
    - pending
    - approved
    - rejected
    type: string
    x-enum-varnames:
    - StatusPending
    - StatusApproved
    - StatusRejected
  recharge.RechargeListResponse:
    properties:
      count:
        type: integer
      requests:
        items:
          $ref: '#/definitions/recharge.RechargeRequest'
        type: array
      total:
        type: integer
    type: object
  recharge.RechargeRequest:
    properties:
      admin_note:
        type: string
      amount:
        description: 充值金额（人民币，单位：元）
        type: number
      created_at:
        type: string
      id:
        type: integer
      order_id:
        type: string
      payment_method:
        type: string
      payment_proof:
        type: string
      points_to_grant:
        description: 根据充值金额换算出的积分
        type: integer
      processed_at:
        type: string
      processed_by:
        type: string
      status:
        $ref: '#/definitions/recharge.ApprovalStatus'
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  recharge.UserRechargeRequest:
    properties:
      amount:
        description: 充值金额（人民币,单位：元）
        minimum: 1
        type: integer
      order_id:
        description: 支付宝订单号
        type: string
      payment_proof:
        description: 支付凭证URL
        type: string
    required:
    - amount
    - order_id
    - payment_proof
    type: object
  response.Response:
    properties:
      code:
        type: string
      data: {}
      error:
        type: string
      message:
        type: string
      success:
        type: boolean
    type: object
  toshandler.TOSCredentials:
    properties:
      access_key_id:
        type: string
      expiration:
        type: string
      secret_access_key:
        type: string
      session_token:
        type: string
    type: object
  toshandler.TOSCredentialsResponse:
    properties:
      bucket:
        type: string
      credentials:
        $ref: '#/definitions/toshandler.TOSCredentials'
      endpoint:
        type: string
      intranet_endpoint:
        type: string
      message:
        type: string
      region:
        type: string
      success:
        type: boolean
    type: object
  wallet.AdjustBalanceRequest:
    properties:
      amount:
        type: integer
      description:
        type: string
      user_id:
        type: string
    required:
    - amount
    - user_id
    type: object
  wallet.BalanceResponse:
    properties:
      free_balance:
        type: integer
      paid_balance:
        type: integer
      total_balance:
        type: integer
    type: object
  wallet.Transaction:
    properties:
      created_at:
        type: string
      description:
        type: string
      free_balance_after:
        type: integer
      free_points_change:
        type: integer
      id:
        type: integer
      metadata:
        type: string
      paid_balance_after:
        type: integer
      paid_points_change:
        type: integer
      related_consumption_id:
        type: string
      related_recharge_id:
        type: integer
      source:
        $ref: '#/definitions/wallet.TransactionSource'
      user_id:
        type: string
    type: object
  wallet.TransactionSource:
    enum:
    - RECHARGE
    - CONSUMPTION
    - GIFT_SIGNUP
    - GIFT_PROMO
    - REFUND
    type: string
    x-enum-comments:
      TransactionSourceConsumption: 业务消费
      TransactionSourceGiftPromo: 活动赠送
      TransactionSourceGiftSignup: 注册赠送
      TransactionSourceRecharge: 付费充值
      TransactionSourceRefund: 退款
    x-enum-descriptions:
    - 付费充值
    - 业务消费
    - 注册赠送
    - 活动赠送
    - 退款
    x-enum-varnames:
    - TransactionSourceRecharge
    - TransactionSourceConsumption
    - TransactionSourceGiftSignup
    - TransactionSourceGiftPromo
    - TransactionSourceRefund
  wallet.TransactionsResponse:
    properties:
      count:
        type: integer
      total:
        type: integer
      transactions:
        items:
          $ref: '#/definitions/wallet.Transaction'
        type: array
    type: object
host: localhost:9000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a serverless AI gateway API server with authentication, wallet
    management, and AI model integration.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Serverless AIG API
  version: "1.0"
paths:
  /:
    get:
      description: 获取服务器状态信息
      produces:
      - text/plain
      responses:
        "200":
          description: Serverless AIG
          schema:
            type: string
      summary: 服务器状态
      tags:
      - 系统
  /api/v1/admin/finance/analysis:
    get:
      description: 管理员权限，按类别分析指定时间段的支出分布和趋势
      parameters:
      - description: 开始日期，格式：YYYY-MM-DD
        example: '"2024-01-01"'
        format: date
        in: query
        name: start_date
        required: true
        type: string
      - description: 结束日期，格式：YYYY-MM-DD
        example: '"2024-01-31"'
        format: date
        in: query
        name: end_date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 支出分析结果，包含各类别支出统计
          schema:
            $ref: '#/definitions/finance.ExpenseAnalysis'
        "400":
          description: 请求参数错误，如日期格式不正确或日期范围无效
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "403":
          description: 权限不足，需要管理员权限
          schema:
            $ref: '#/definitions/models.SwaggerForbiddenErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取支出分析
      tags:
      - 管理员财务
  /api/v1/admin/finance/expense:
    post:
      consumes:
      - application/json
      description: 管理员权限，用于录入一笔新的运营支出，包括服务器、AI模型调用等各类成本
      parameters:
      - description: 支出详情
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/finance.AddExpenseRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 支出记录创建成功
          schema:
            $ref: '#/definitions/models.SwaggerEmptyResponse'
        "400":
          description: 请求参数错误，如金额格式不正确或类别无效
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "403":
          description: 权限不足，需要管理员权限
          schema:
            $ref: '#/definitions/models.SwaggerForbiddenErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 新增支出记录
      tags:
      - 管理员财务
  /api/v1/admin/finance/expenses:
    get:
      description: 管理员权限，获取支出记录列表
      parameters:
      - description: 支出类别
        in: query
        name: category
        type: string
      - description: 开始日期
        format: date
        in: query
        name: start_date
        type: string
      - description: 结束日期
        format: date
        in: query
        name: end_date
        type: string
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 支出列表
          schema:
            $ref: '#/definitions/finance.ExpenseListResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: 获取支出列表
      tags:
      - 管理员财务
  /api/v1/admin/finance/report:
    post:
      consumes:
      - application/json
      description: 管理员权限，获取指定时间段的综合财务报表，包含收入、支出、利润和用户指标
      parameters:
      - description: 报表请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/finance.FinancialReportRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 财务报表，包含收入支出明细和用户指标
          schema:
            $ref: '#/definitions/finance.FinancialReport'
        "400":
          description: 请求参数错误，如日期格式不正确
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "403":
          description: 权限不足，需要管理员权限
          schema:
            $ref: '#/definitions/models.SwaggerForbiddenErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取财务报表
      tags:
      - 管理员财务
  /api/v1/admin/recharge/process:
    post:
      consumes:
      - application/json
      description: 管理员批准或拒绝充值申请，可以修改实际充值金额，批准后自动添加积分到用户钱包
      parameters:
      - description: 处理请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/recharge.AdminProcessRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 充值申请处理成功
          schema:
            $ref: '#/definitions/models.SwaggerEmptyResponse'
        "400":
          description: 请求参数错误，如申请ID无效或操作类型不正确
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "403":
          description: 权限不足，需要管理员权限
          schema:
            $ref: '#/definitions/models.SwaggerForbiddenErrorResponse'
        "404":
          description: 充值申请不存在
          schema:
            $ref: '#/definitions/models.SwaggerNotFoundErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 处理充值申请
      tags:
      - 管理员充值
  /api/v1/admin/recharge/requests:
    get:
      description: 管理员获取所有用户的充值申请列表
      parameters:
      - description: 申请状态
        enum:
        - pending
        - approved
        - rejected
        in: query
        name: status
        type: string
      - default: 50
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 充值申请列表
          schema:
            $ref: '#/definitions/recharge.RechargeListResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: 获取充值申请列表
      tags:
      - 管理员充值
  /api/v1/admin/user/balance/adjust:
    post:
      consumes:
      - application/json
      description: 管理员手动调整用户余额，仅用于添加免费积分或赠送积分，不支持扣减操作
      parameters:
      - description: 调整余额请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/wallet.AdjustBalanceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 余额调整成功
          schema:
            $ref: '#/definitions/models.SwaggerEmptyResponse'
        "400":
          description: 请求参数错误，如用户ID格式不正确或金额无效
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "403":
          description: 权限不足，需要管理员权限
          schema:
            $ref: '#/definitions/models.SwaggerForbiddenErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 调整用户余额
      tags:
      - 管理员用户
  /api/v1/admin/user/create:
    post:
      consumes:
      - application/json
      description: 管理员创建新用户
      parameters:
      - description: 用户信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.CreateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 用户创建成功
          schema:
            $ref: '#/definitions/auth.AuthResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: 创建用户
      tags:
      - 管理员用户
  /api/v1/admin/user/info:
    get:
      description: 管理员获取指定用户的详细信息
      parameters:
      - description: 用户ID
        in: query
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户信息
          schema:
            $ref: '#/definitions/auth.User'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: 获取用户信息
      tags:
      - 管理员用户
  /api/v1/admin/user/list:
    get:
      description: 管理员获取系统中所有用户的列表
      parameters:
      - description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 偏移量
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 用户列表
          schema:
            $ref: '#/definitions/auth.UserListResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/response.Response'
      security:
      - BearerAuth: []
      summary: 获取用户列表
      tags:
      - 管理员用户
  /api/v1/ai/configs:
    get:
      description: 获取系统AI分析配置列表，包含可用的分析模式和参数设置
      produces:
      - application/json
      responses:
        "200":
          description: AI配置列表，包含模式名称、描述和参数
          schema:
            items:
              $ref: '#/definitions/ai.ConfigItem'
            type: array
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取配置列表
      tags:
      - 人工智能
  /api/v1/ai/subjects:
    get:
      description: 获取系统支持的学科列表，用于AI分析时选择对应的评分标准
      produces:
      - application/json
      responses:
        "200":
          description: 学科列表，包含学科键值和描述信息
          schema:
            items:
              $ref: '#/definitions/ai.Subject'
            type: array
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取学科列表
      tags:
      - 人工智能
  /api/v1/auth/email-code:
    post:
      consumes:
      - application/json
      description: 向用户邮箱发送验证码
      parameters:
      - description: 邮箱地址
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.EmailCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 验证码发送成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 发送邮箱验证码
      tags:
      - 用户认证
  /api/v1/auth/email-login:
    post:
      consumes:
      - application/json
      description: 使用邮箱验证码登录
      parameters:
      - description: 邮箱和验证码
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.EmailLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/auth.AuthResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.Response'
      summary: 邮箱验证码登录
      tags:
      - 用户认证
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      description: 使用邮箱和密码登录系统，成功后返回访问令牌和用户信息
      parameters:
      - description: 登录信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回访问令牌和用户信息
          schema:
            $ref: '#/definitions/auth.AuthResponse'
        "400":
          description: 请求参数错误，如邮箱格式不正确或密码长度不足
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
        "401":
          description: 认证失败，邮箱或密码错误
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
      summary: 用户登录
      tags:
      - 用户认证
  /api/v1/auth/recover:
    post:
      consumes:
      - application/json
      description: 向用户邮箱发送密码重置邮件，用户需要从邮件中复制令牌进行密码重置
      parameters:
      - description: 邮箱地址
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.RecoverRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 密码重置邮件发送成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误，如邮箱格式不正确或用户不存在
          schema:
            $ref: '#/definitions/response.Response'
      summary: 发送密码重置邮件
      tags:
      - 用户认证
  /api/v1/auth/refresh:
    post:
      consumes:
      - application/json
      description: 使用刷新令牌获取新的访问令牌，延长用户会话时间
      parameters:
      - description: 刷新令牌信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 刷新成功，返回新的访问令牌
          schema:
            $ref: '#/definitions/auth.AuthResponse'
        "400":
          description: 请求参数错误或刷新令牌无效
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
      summary: 刷新访问令牌
      tags:
      - 用户认证
  /api/v1/auth/register:
    post:
      consumes:
      - application/json
      description: 使用邮箱和密码注册新用户，自动创建钱包并赠送500积分
      parameters:
      - description: 注册信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.RegisterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功，返回访问令牌和用户信息
          schema:
            $ref: '#/definitions/auth.AuthResponse'
        "400":
          description: 请求参数错误，如邮箱已存在或密码格式不正确
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
      summary: 用户注册
      tags:
      - 用户认证
  /api/v1/auth/update-password:
    post:
      consumes:
      - application/json
      description: 使用密码重置令牌更新用户密码，无需登录状态
      parameters:
      - description: 新密码信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.UpdatePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 密码更新成功
          schema:
            $ref: '#/definitions/response.Response'
        "400":
          description: 请求参数错误，如密码格式不符合要求
          schema:
            $ref: '#/definitions/response.Response'
        "401":
          description: 未授权或令牌无效
          schema:
            $ref: '#/definitions/response.Response'
      summary: 更新密码
      tags:
      - 用户认证
  /api/v1/auth/verify:
    post:
      consumes:
      - application/json
      description: 验证邮箱验证令牌或密码重置令牌，支持多种验证类型
      parameters:
      - description: 验证信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/auth.VerifyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 验证成功，返回用户信息
          schema:
            $ref: '#/definitions/auth.AuthResponse'
        "400":
          description: 请求参数错误或令牌无效/过期
          schema:
            $ref: '#/definitions/response.Response'
      summary: 验证令牌
      tags:
      - 用户认证
  /api/v1/protected:
    get:
      description: 测试认证是否正常工作的受保护端点
      produces:
      - application/json
      responses:
        "200":
          description: 认证成功，返回用户信息
          schema:
            $ref: '#/definitions/models.SwaggerEmptyResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
      security:
      - BearerAuth: []
      summary: 受保护的API测试
      tags:
      - 系统
  /api/v1/recharge/list:
    get:
      description: 获取当前用户的充值申请列表，包含申请状态、金额、处理时间等信息
      parameters:
      - default: 20
        description: 每页数量，最大100
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: 偏移量，用于分页
        in: query
        minimum: 0
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 充值申请列表，包含申请详情和分页信息
          schema:
            $ref: '#/definitions/recharge.RechargeListResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取用户充值申请列表
      tags:
      - 用户充值
  /api/v1/recharge/request:
    post:
      consumes:
      - application/json
      description: 用户提交充值申请，需要提供支付凭证和订单信息，按1:1000比例转换为积分
      parameters:
      - description: 充值申请信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/recharge.UserRechargeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 充值申请提交成功，等待管理员审核
          schema:
            $ref: '#/definitions/models.SwaggerEmptyResponse'
        "400":
          description: 请求参数错误，如金额无效或支付凭证格式不正确
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 提交充值申请
      tags:
      - 用户充值
  /api/v1/tos/credentials:
    get:
      description: 获取访问火山引擎对象存储的临时凭证，用于上传图片等文件，凭证有效期1小时
      produces:
      - application/json
      responses:
        "200":
          description: 临时凭证信息，包含访问密钥、会话令牌和存储桶信息
          schema:
            $ref: '#/definitions/toshandler.TOSCredentialsResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "500":
          description: 服务器内部错误或STS服务不可用
          schema:
            $ref: '#/definitions/models.SwaggerServiceUnavailableErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取TOS临时凭证
      tags:
      - 文件存储
  /api/v1/version:
    get:
      description: 获取当前应用的版本信息，包括版本号、构建时间等
      produces:
      - application/json
      responses:
        "200":
          description: 应用版本信息，包含版本号和构建信息
          schema:
            $ref: '#/definitions/config.AppConfig'
      summary: 获取应用版本信息
      tags:
      - 系统
  /api/v1/wallet/balance:
    get:
      description: 获取当前用户的钱包余额信息，包括付费积分和免费积分
      produces:
      - application/json
      responses:
        "200":
          description: 余额信息，包含付费积分、免费积分和总余额
          schema:
            $ref: '#/definitions/wallet.BalanceResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 查询钱包余额
      tags:
      - 用户钱包
  /api/v1/wallet/transactions:
    get:
      description: 获取当前用户的交易记录列表，包括充值、消费、赠送等所有交易类型
      parameters:
      - default: 20
        description: 每页数量，最大100
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: 偏移量，用于分页
        in: query
        minimum: 0
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 交易记录列表，包含交易详情和分页信息
          schema:
            $ref: '#/definitions/wallet.TransactionsResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.SwaggerInternalServerErrorResponse'
      security:
      - BearerAuth: []
      summary: 查询交易记录
      tags:
      - 用户钱包
  /api/v2/chat/analysis:
    post:
      consumes:
      - application/json
      description: 使用AI模型分析图像内容，支持多种分析模式：专业模式(OCR+分析)、智能模式、经济模式
      parameters:
      - description: 分析请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/ai.AnalysisRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 分析结果，包含学生答案、得分和详细评分说明
          schema:
            $ref: '#/definitions/ai.AnalysisResponse'
        "400":
          description: 请求参数错误，如图像URL无效或分析模式不支持
          schema:
            $ref: '#/definitions/models.SwaggerValidationErrorResponse'
        "401":
          description: 未授权，需要有效的访问令牌
          schema:
            $ref: '#/definitions/models.SwaggerUnauthorizedErrorResponse'
        "402":
          description: 余额不足，无法完成分析
          schema:
            $ref: '#/definitions/models.SwaggerInsufficientBalanceErrorResponse'
        "500":
          description: 服务器内部错误或AI服务不可用
          schema:
            $ref: '#/definitions/models.SwaggerServiceUnavailableErrorResponse'
      security:
      - BearerAuth: []
      summary: AI图像分析
      tags:
      - 人工智能
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: X-Access-Token
    type: apiKey
swagger: "2.0"
