package router

import (
	"net/http"

	"serverless-aig/internal/config"
	"serverless-aig/internal/container"
	"serverless-aig/internal/middleware"

	_ "serverless-aig/docs"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// protectedHandler 受保护的API测试
// @Summary 受保护的API测试
// @Description 测试认证是否正常工作的受保护端点
// @Tags 系统
// @Security BearerAuth
// @Produce json
// @Success 200 {object} models.SwaggerEmptyResponse "认证成功，返回用户信息"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Router /api/v1/protected [get]
func protectedHandler(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	c.JSON(http.StatusOK, gin.H{
		"message": "您已成功通过身份验证",
		"user":    user,
	})
}

// healthHandler 健康检查
// @Summary 服务器状态
// @Description 获取服务器状态信息
// @Tags 系统
// @Produce plain
// @Success 200 {string} string "Serverless AIG"
// @Router / [get]
func healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "Serverless AIG")
}

// New 创建新的路由器
func New(cfg *config.Config, container *container.Container) *gin.Engine {
	router := gin.Default()

	// CORS中间件
	router.Use(corsMiddleware())

	// Swagger文档路由（如果启用）
	if cfg.Server.SwaggerEnabled {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	// 健康检查
	router.GET("/", healthHandler)

	// 版本信息
	versionHandler := container.VersionHandler()
	router.GET("/api/v1/version", versionHandler.GetVersion)

	// 公共路由
	setupPublicRoutes(router, container)

	// 受保护的路由
	setupProtectedRoutes(router, container, cfg)

	return router
}

// setupPublicRoutes 设置公共路由
func setupPublicRoutes(router *gin.Engine, container *container.Container) {
	authHandler := container.AuthHandler()

	// 认证相关路由
	router.POST("/api/v1/auth/login", authHandler.Login)
	router.POST("/api/v1/auth/register", authHandler.Register)
	router.POST("/api/v1/auth/verify", authHandler.Verify)
	router.POST("/api/v1/auth/recover", authHandler.Recover)
	router.POST("/api/v1/auth/update-password", authHandler.UpdatePassword)
}

// setupProtectedRoutes 设置受保护的路由
func setupProtectedRoutes(router *gin.Engine, container *container.Container, cfg *config.Config) {
	// 认证中间件
	authMiddleware := middleware.Auth(container.SupabaseClient())

	// 受保护的路由组
	protected := router.Group("/")
	protected.Use(authMiddleware)

	// 测试路由
	protected.GET("/api/v1/protected", protectedHandler)

	// 钱包相关路由（两种模式都需要）
	walletHandler := container.WalletHandler()
	protected.GET("/api/v1/wallet/balance", walletHandler.GetBalance)
	protected.GET("/api/v1/wallet/transactions", walletHandler.GetTransactions)

	// 根据服务模式添加不同的路由
	if cfg.ServiceMode == "user" || cfg.ServiceMode == "" {
		setupUserRoutes(protected, container)
	}

	if cfg.ServiceMode == "admin" || cfg.ServiceMode == "" {
		setupAdminRoutes(protected, container, cfg)
	}
}

// setupUserRoutes 设置用户路由
func setupUserRoutes(group *gin.RouterGroup, container *container.Container) {
	aiHandler := container.AIHandler()
	rechargeHandler := container.RechargeHandler()
	tosHandler := container.TOSHandler()

	// AI分析路由
	group.POST("/api/v2/chat/analysis", aiHandler.Analysis)
	group.GET("/api/v1/ai/configs", aiHandler.GetConfigs)
	group.GET("/api/v1/ai/subjects", aiHandler.GetSubjects)

	// 用户充值路由
	group.POST("/api/v1/recharge/request", rechargeHandler.UserRequest)
	group.GET("/api/v1/recharge/list", rechargeHandler.UserList)

	// TOS凭证路由
	group.GET("/api/v1/tos/credentials", tosHandler.GetCredentials)
}

// setupAdminRoutes 设置管理员路由
func setupAdminRoutes(group *gin.RouterGroup, container *container.Container, cfg *config.Config) {
	rechargeHandler := container.RechargeHandler()
	authHandler := container.AuthHandler()
	financeHandler := container.FinanceHandler()
	walletHandler := container.WalletHandler()

	// 管理员中间件
	adminMiddleware := middleware.Admin(cfg)

	// 充值管理路由
	group.GET("/api/v1/admin/recharge/requests", adminMiddleware, rechargeHandler.AdminList)
	group.POST("/api/v1/admin/recharge/process", adminMiddleware, rechargeHandler.AdminProcess)

	// 用户管理路由
	group.GET("/api/v1/admin/user/info", adminMiddleware, authHandler.AdminGetUserInfo)
	group.POST("/api/v1/admin/user/balance/adjust", adminMiddleware, walletHandler.AdminAdjustBalance)
	group.GET("/api/v1/admin/user/list", adminMiddleware, authHandler.AdminGetUserList)
	group.POST("/api/v1/admin/user/create", adminMiddleware, authHandler.AdminCreateUser)

	// 财务管理路由
	group.POST("/api/v1/admin/finance/expense", adminMiddleware, financeHandler.AddExpense)
	group.GET("/api/v1/admin/finance/report", adminMiddleware, financeHandler.GetReport)
	group.GET("/api/v1/admin/finance/analysis", adminMiddleware, financeHandler.GetAnalysis)
	group.GET("/api/v1/admin/finance/expenses", adminMiddleware, financeHandler.ListExpenses)
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
		c.Header("Access-Control-Allow-Headers", "*")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		c.Next()
	}
}
