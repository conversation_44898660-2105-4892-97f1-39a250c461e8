package middleware

import (
	"context"
	"net/http"
	"serverless-aig/internal/client/supabase"
	"serverless-aig/internal/config"
	"slices"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/supabase-community/auth-go/types"
)

// contextKey 上下文键类型
type contextKey string

// 用户上下文键
const userContextKey contextKey = "user"

// Auth 认证中间件
func Auth(supabaseClient *supabase.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// 尝试从X-Access-Token头获取
			authHeader = c.GetHeader("X-Access-Token")
		}

		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证令牌"})
			c.Abort()
			return
		}

		// 提取Bearer令牌
		token := extractBearerToken(authHeader)
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的认证令牌格式"})
			c.Abort()
			return
		}

		// 验证令牌
		user, err := supabaseClient.GetUser(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败"})
			c.Abort()
			return
		}

		// 将用户信息添加到上下文
		ctx := withUser(c.Request.Context(), user)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

// Admin 管理员权限中间件
func Admin(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		user := GetUserFromContext(c)
		if user == nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
			c.Abort()
			return
		}

		if !isAdmin(user.ID.String(), cfg) {
			c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// extractBearerToken 提取Bearer令牌
func extractBearerToken(authHeader string) string {
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 {
		return authHeader // 如果没有Bearer前缀，直接返回整个字符串
	}

	if strings.ToLower(parts[0]) != "bearer" {
		return authHeader // 如果不是Bearer，直接返回整个字符串
	}

	return parts[1]
}

// withUser 将用户信息添加到上下文中
func withUser(ctx context.Context, user *types.UserResponse) context.Context {
	return context.WithValue(ctx, userContextKey, user)
}

// GetUserFromContext 从上下文中获取用户信息
func GetUserFromContext(c *gin.Context) *types.UserResponse {
	user, ok := c.Request.Context().Value(userContextKey).(*types.UserResponse)
	if !ok {
		return nil
	}
	return user
}

// UserFromContext 从上下文中获取用户信息（兼容旧代码）
func UserFromContext(ctx context.Context) (*types.UserResponse, bool) {
	user, ok := ctx.Value(userContextKey).(*types.UserResponse)
	return user, ok
}

// isAdmin 检查用户是否为管理员
func isAdmin(userID string, cfg *config.Config) bool {
	// 从配置中获取管理员ID列表
	adminIDs := cfg.App.AdminIDs

	// 如果配置中没有设置管理员ID，返回false
	if len(adminIDs) == 0 {
		return false
	}

	return slices.Contains(adminIDs, userID)
}
