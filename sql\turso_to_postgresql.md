# Turso 数据库迁移到 PostgreSQL

## 第一步：从 Turso 导出数据

```bash
# 安装 Turso CLI (如果未安装)
curl -sSfL https://get.tur.so/install.sh | bash
# 登录到 Turso
turso auth login --headless

# 导出数据库
# 方法一：使用 Turso CLI export 命令
turso db export aig
# ------------
# 方法二：使用 Turso CLI db shell 命令
turso db shell aig .dump > dump.sql
# 将 SQL 文件转换为 SQLite 数据库文件
sqlite3 aig.db < dump.sql
```

## 第二步：创建数据库并给新用户分配权限

清理(可选)和重新创建

```sql
-- ====================================================================
-- PART 1: 清理和删除 (Cleanup & Deletion)
-- ====================================================================

-- 步骤 1: 强制断开所有到 "aig" 数据库的连接
-- 这是为了确保 DROP DATABASE 命令不会因为有活动连接而失败。
SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'aig';

-- 步骤 2: 删除数据库
-- 使用 IF EXISTS 可以避免在数据库不存在时报错，让脚本可以重复执行。
DROP DATABASE IF EXISTS aig;

-- 步骤 3: 删除用户
-- 同样使用 IF EXISTS 避免报错。
DROP USER IF EXISTS aig_user;

-- ====================================================================
-- PART 2: 重新创建和授权 (Re-creation & Authorization)
-- ====================================================================

-- 步骤 1: 创建新的用户
-- 请务必在这里设置一个强大且安全的密码！
CREATE USER aig_user WITH PASSWORD '在这里输入一个强大的新密码';

-- 步骤 2: 创建新的数据库，并直接指定所有者为 aig_user
-- 这是最简洁的方式，在创建时就赋予所有权。
CREATE DATABASE aig OWNER aig_user;

-- 步骤 3: (可选但推荐) 授予用户在该数据库上的所有权限
-- 这确保 aig_user 不仅是所有者，还能在该数据库上创建 schema 等。
GRANT ALL PRIVILEGES ON DATABASE aig TO aig_user;


-- ====================================================================
-- 操作完成
-- ====================================================================
-- 提示：现在你的环境已经准备就绪。
-- 你可以切换到使用 aig_user 的 DBeaver 连接来操作 aig 数据库了。
```

## 第三步：迁移数据到 PostgreSQL

```bash
# TODO
```

## 导出整个 Schema 的结构（不含数据）

```bash
pg_dump -h localhost -U aig_user -n public --schema-only -d aig > public_schema.sql
```
