# 管理员手动调整余额API

## 功能说明

`ManualAdjustBalance` 方法专门用于管理员给用户增加赠送积分，常用于活动奖励、补偿等场景。

## 方法签名

```go
func (p *PostgreSQLOperations) ManualAdjustBalance(userID string, amount int, adminID string, reason string) (int, error)
```

## 参数说明

- `userID`: 用户ID（必填）
- `amount`: 要增加的赠送积分数量，必须为正数（必填）
- `adminID`: 执行操作的管理员ID（必填）
- `reason`: 调整原因说明（必填）

## 返回值

- `int`: 调整后的用户总余额（付费积分 + 赠送积分）
- `error`: 错误信息，如果操作成功则为 nil

## 功能特点

1. **只能增加积分**: 该方法只接受正数，专门用于给用户增加赠送积分
2. **钱包检查**: 如果用户钱包不存在，会返回错误提示用户先注册登录
3. **原子性操作**: 使用CTE（Common Table Expression）确保钱包更新和交易记录在单个原子操作中完成
4. **完整记录**: 自动记录交易历史，包含管理员信息和操作原因

## 数据库操作

使用CTE实现原子性操作：

```sql
WITH wallet_check AS (
    SELECT user_id, paid_balance, free_balance
    FROM wallets
    WHERE user_id = $1
),
updated_wallet AS (
    UPDATE wallets
    SET free_balance = free_balance + $2, updated_at = $3
    WHERE user_id = $1 AND EXISTS(SELECT 1 FROM wallet_check)
    RETURNING user_id, paid_balance, free_balance
)
INSERT INTO transactions (
    user_id, source, paid_points_change, free_points_change,
    paid_balance_after, free_balance_after, description, metadata, created_at
)
SELECT
    uw.user_id, 'GIFT_PROMO'::transaction_source, 0, $2,
    uw.paid_balance, uw.free_balance, $4, $5::jsonb, $6
FROM updated_wallet uw
RETURNING (paid_balance_after + free_balance_after) AS total_balance;
```

## 使用示例

### 基本用法

```go
// 给用户增加1000赠送积分
newBalance, err := pg.ManualAdjustBalance(
    "user-uuid-123",           // 用户ID
    1000,                      // 增加1000积分
    "admin-uuid-456",          // 管理员ID
    "活动奖励",                 // 调整原因
)
if err != nil {
    log.Printf("调整余额失败: %v", err)
    return
}
log.Printf("调整成功，用户新余额: %d", newBalance)
```

### HTTP API 调用示例

```bash
curl -X POST http://localhost:8080/api/v1/admin/user/balance/adjust \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-admin-token" \
  -d '{
    "user_id": "user-uuid-123",
    "amount": 1000,
    "reason": "活动奖励"
  }'
```

### 响应示例

```json
{
  "success": true,
  "new_balance": 15000,
  "message": "用户余额调整成功"
}
```

## 错误处理

### 常见错误

1. **积分数量无效**
   ```
   amount must be positive for gift points
   ```

2. **数据库连接失败**
   ```
   failed to get database connection: connection refused
   ```

3. **用户钱包不存在**
   ```
   user wallet does not exist, please register and login first
   ```

### 错误处理示例

```go
newBalance, err := pg.ManualAdjustBalance(userID, amount, adminID, reason)
if err != nil {
    switch {
    case strings.Contains(err.Error(), "positive"):
        return fmt.Errorf("积分数量必须为正数")
    case strings.Contains(err.Error(), "connection"):
        return fmt.Errorf("数据库连接失败，请稍后重试")
    default:
        return fmt.Errorf("调整余额失败: %v", err)
    }
}
```

## 注意事项

1. **权限控制**: 调用此方法前需要验证管理员权限
2. **积分类型**: 此方法只增加赠送积分，不影响付费积分
3. **消费优先级**: 用户消费时会优先使用赠送积分
4. **钱包检查**: 用户必须先注册并登录创建钱包，否则操作会失败
5. **原子性**: 使用CTE确保钱包更新和交易记录的原子性，避免数据不一致
6. **审计追踪**: 所有操作都有完整的审计记录，包含操作时间、管理员和原因
7. **幂等性**: 重复调用会重复增加积分，请在业务层控制重复操作

## 相关方法

- `GetUserBalance(userID)`: 获取用户总余额
- `GetUserDetailedBalance(userID)`: 获取用户详细余额（分别显示付费和赠送积分）
- `GetUserTransactionRecordsNew(userID, limit, offset)`: 获取用户交易记录
