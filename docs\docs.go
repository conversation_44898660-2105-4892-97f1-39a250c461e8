// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/": {
            "get": {
                "description": "获取服务器状态信息",
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "系统"
                ],
                "summary": "服务器状态",
                "responses": {
                    "200": {
                        "description": "Serverless AIG",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/finance/analysis": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员权限，按类别分析指定时间段的支出分布和趋势",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员财务"
                ],
                "summary": "获取支出分析",
                "parameters": [
                    {
                        "type": "string",
                        "format": "date",
                        "example": "\"2024-01-01\"",
                        "description": "开始日期，格式：YYYY-MM-DD",
                        "name": "start_date",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "date",
                        "example": "\"2024-01-31\"",
                        "description": "结束日期，格式：YYYY-MM-DD",
                        "name": "end_date",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "支出分析结果，包含各类别支出统计",
                        "schema": {
                            "$ref": "#/definitions/finance.ExpenseAnalysis"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如日期格式不正确或日期范围无效",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足，需要管理员权限",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerForbiddenErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/finance/expense": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员权限，用于录入一笔新的运营支出，包括服务器、AI模型调用等各类成本",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员财务"
                ],
                "summary": "新增支出记录",
                "parameters": [
                    {
                        "description": "支出详情",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/finance.AddExpenseRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "支出记录创建成功",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerEmptyResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如金额格式不正确或类别无效",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足，需要管理员权限",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerForbiddenErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/finance/expenses": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员权限，获取支出记录列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员财务"
                ],
                "summary": "获取支出列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "支出类别",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "format": "date",
                        "description": "开始日期",
                        "name": "start_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "format": "date",
                        "description": "结束日期",
                        "name": "end_date",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "支出列表",
                        "schema": {
                            "$ref": "#/definitions/finance.ExpenseListResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/finance/report": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员权限，获取指定时间段的综合财务报表，包含收入、支出、利润和用户指标",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员财务"
                ],
                "summary": "获取财务报表",
                "parameters": [
                    {
                        "description": "报表请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/finance.FinancialReportRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "财务报表，包含收入支出明细和用户指标",
                        "schema": {
                            "$ref": "#/definitions/finance.FinancialReport"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如日期格式不正确",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足，需要管理员权限",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerForbiddenErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/recharge/process": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员批准或拒绝充值申请，可以修改实际充值金额，批准后自动添加积分到用户钱包",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员充值"
                ],
                "summary": "处理充值申请",
                "parameters": [
                    {
                        "description": "处理请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/recharge.AdminProcessRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "充值申请处理成功",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerEmptyResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如申请ID无效或操作类型不正确",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足，需要管理员权限",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerForbiddenErrorResponse"
                        }
                    },
                    "404": {
                        "description": "充值申请不存在",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerNotFoundErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/recharge/requests": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员获取所有用户的充值申请列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员充值"
                ],
                "summary": "获取充值申请列表",
                "parameters": [
                    {
                        "enum": [
                            "pending",
                            "approved",
                            "rejected"
                        ],
                        "type": "string",
                        "description": "申请状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 50,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "充值申请列表",
                        "schema": {
                            "$ref": "#/definitions/recharge.RechargeListResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/user/balance/adjust": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员手动调整用户余额，仅用于添加免费积分或赠送积分，不支持扣减操作",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员用户"
                ],
                "summary": "调整用户余额",
                "parameters": [
                    {
                        "description": "调整余额请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/wallet.AdjustBalanceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "余额调整成功",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerEmptyResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如用户ID格式不正确或金额无效",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足，需要管理员权限",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerForbiddenErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/user/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员创建新用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员用户"
                ],
                "summary": "创建用户",
                "parameters": [
                    {
                        "description": "用户信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.CreateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户创建成功",
                        "schema": {
                            "$ref": "#/definitions/auth.AuthResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/user/info": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员获取指定用户的详细信息",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员用户"
                ],
                "summary": "获取用户信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户信息",
                        "schema": {
                            "$ref": "#/definitions/auth.User"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/admin/user/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员获取系统中所有用户的列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "管理员用户"
                ],
                "summary": "获取用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户列表",
                        "schema": {
                            "$ref": "#/definitions/auth.UserListResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/ai/configs": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统AI分析配置列表，包含可用的分析模式和参数设置",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "人工智能"
                ],
                "summary": "获取配置列表",
                "responses": {
                    "200": {
                        "description": "AI配置列表，包含模式名称、描述和参数",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/ai.ConfigItem"
                            }
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/ai/subjects": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统支持的学科列表，用于AI分析时选择对应的评分标准",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "人工智能"
                ],
                "summary": "获取学科列表",
                "responses": {
                    "200": {
                        "description": "学科列表，包含学科键值和描述信息",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/ai.Subject"
                            }
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/email-code": {
            "post": {
                "description": "向用户邮箱发送验证码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "发送邮箱验证码",
                "parameters": [
                    {
                        "description": "邮箱地址",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.EmailCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "验证码发送成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/email-login": {
            "post": {
                "description": "使用邮箱验证码登录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "邮箱验证码登录",
                "parameters": [
                    {
                        "description": "邮箱和验证码",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.EmailLoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功",
                        "schema": {
                            "$ref": "#/definitions/auth.AuthResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/login": {
            "post": {
                "description": "使用邮箱和密码登录系统，成功后返回访问令牌和用户信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "登录信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功，返回访问令牌和用户信息",
                        "schema": {
                            "$ref": "#/definitions/auth.AuthResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如邮箱格式不正确或密码长度不足",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    },
                    "401": {
                        "description": "认证失败，邮箱或密码错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/recover": {
            "post": {
                "description": "向用户邮箱发送密码重置邮件，用户需要从邮件中复制令牌进行密码重置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "发送密码重置邮件",
                "parameters": [
                    {
                        "description": "邮箱地址",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.RecoverRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码重置邮件发送成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如邮箱格式不正确或用户不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/refresh": {
            "post": {
                "description": "使用刷新令牌获取新的访问令牌，延长用户会话时间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "刷新访问令牌",
                "parameters": [
                    {
                        "description": "刷新令牌信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.RefreshTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "刷新成功，返回新的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/auth.AuthResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误或刷新令牌无效",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/register": {
            "post": {
                "description": "使用邮箱和密码注册新用户，自动创建钱包并赠送500积分",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "用户注册",
                "parameters": [
                    {
                        "description": "注册信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.RegisterRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注册成功，返回访问令牌和用户信息",
                        "schema": {
                            "$ref": "#/definitions/auth.AuthResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如邮箱已存在或密码格式不正确",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/update-password": {
            "post": {
                "description": "使用密码重置令牌更新用户密码，无需登录状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "更新密码",
                "parameters": [
                    {
                        "description": "新密码信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.UpdatePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码更新成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如密码格式不符合要求",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权或令牌无效",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/verify": {
            "post": {
                "description": "验证邮箱验证令牌或密码重置令牌，支持多种验证类型",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "验证令牌",
                "parameters": [
                    {
                        "description": "验证信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/auth.VerifyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "验证成功，返回用户信息",
                        "schema": {
                            "$ref": "#/definitions/auth.AuthResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误或令牌无效/过期",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/api/v1/protected": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "测试认证是否正常工作的受保护端点",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统"
                ],
                "summary": "受保护的API测试",
                "responses": {
                    "200": {
                        "description": "认证成功，返回用户信息",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerEmptyResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/recharge/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的充值申请列表，包含申请状态、金额、处理时间等信息",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户充值"
                ],
                "summary": "获取用户充值申请列表",
                "parameters": [
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量，最大100",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量，用于分页",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "充值申请列表，包含申请详情和分页信息",
                        "schema": {
                            "$ref": "#/definitions/recharge.RechargeListResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/recharge/request": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "用户提交充值申请，需要提供支付凭证和订单信息，按1:1000比例转换为积分",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户充值"
                ],
                "summary": "提交充值申请",
                "parameters": [
                    {
                        "description": "充值申请信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/recharge.UserRechargeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "充值申请提交成功，等待管理员审核",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerEmptyResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如金额无效或支付凭证格式不正确",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/tos/credentials": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取访问火山引擎对象存储的临时凭证，用于上传图片等文件，凭证有效期1小时",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "文件存储"
                ],
                "summary": "获取TOS临时凭证",
                "responses": {
                    "200": {
                        "description": "临时凭证信息，包含访问密钥、会话令牌和存储桶信息",
                        "schema": {
                            "$ref": "#/definitions/toshandler.TOSCredentialsResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误或STS服务不可用",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerServiceUnavailableErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/version": {
            "get": {
                "description": "获取当前应用的版本信息，包括版本号、构建时间等",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统"
                ],
                "summary": "获取应用版本信息",
                "responses": {
                    "200": {
                        "description": "应用版本信息，包含版本号和构建信息",
                        "schema": {
                            "$ref": "#/definitions/config.AppConfig"
                        }
                    }
                }
            }
        },
        "/api/v1/wallet/balance": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的钱包余额信息，包括付费积分和免费积分",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户钱包"
                ],
                "summary": "查询钱包余额",
                "responses": {
                    "200": {
                        "description": "余额信息，包含付费积分、免费积分和总余额",
                        "schema": {
                            "$ref": "#/definitions/wallet.BalanceResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/wallet/transactions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的交易记录列表，包括充值、消费、赠送等所有交易类型",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户钱包"
                ],
                "summary": "查询交易记录",
                "parameters": [
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量，最大100",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量，用于分页",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "交易记录列表，包含交易详情和分页信息",
                        "schema": {
                            "$ref": "#/definitions/wallet.TransactionsResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInternalServerErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v2/chat/analysis": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "使用AI模型分析图像内容，支持多种分析模式：专业模式(OCR+分析)、智能模式、经济模式",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "人工智能"
                ],
                "summary": "AI图像分析",
                "parameters": [
                    {
                        "description": "分析请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ai.AnalysisRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分析结果，包含学生答案、得分和详细评分说明",
                        "schema": {
                            "$ref": "#/definitions/ai.AnalysisResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误，如图像URL无效或分析模式不支持",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerValidationErrorResponse"
                        }
                    },
                    "401": {
                        "description": "未授权，需要有效的访问令牌",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerUnauthorizedErrorResponse"
                        }
                    },
                    "402": {
                        "description": "余额不足，无法完成分析",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerInsufficientBalanceErrorResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误或AI服务不可用",
                        "schema": {
                            "$ref": "#/definitions/models.SwaggerServiceUnavailableErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "ai.AnalysisMode": {
            "type": "string",
            "enum": [
                "traditional",
                "thinking",
                "standard",
                "economy"
            ],
            "x-enum-varnames": [
                "TraditionalMode",
                "ThinkingMode",
                "StandardMode",
                "EconomyMode"
            ]
        },
        "ai.AnalysisRequest": {
            "type": "object",
            "required": [
                "content",
                "prompt_key",
                "text"
            ],
            "properties": {
                "analysis_mode": {
                    "description": "分析模式",
                    "allOf": [
                        {
                            "$ref": "#/definitions/ai.AnalysisMode"
                        }
                    ]
                },
                "content": {
                    "description": "学生答案图片url",
                    "type": "string"
                },
                "prompt_key": {
                    "description": "系统提示词的键值",
                    "type": "string"
                },
                "text": {
                    "description": "评分标准",
                    "type": "string"
                }
            }
        },
        "ai.AnalysisResponse": {
            "type": "object",
            "properties": {
                "balance": {
                    "$ref": "#/definitions/ai.BalanceInfo"
                },
                "cost_info": {
                    "$ref": "#/definitions/ai.CostInfo"
                },
                "mode": {
                    "$ref": "#/definitions/ai.AnalysisMode"
                },
                "models": {
                    "$ref": "#/definitions/ai.ModelsUsed"
                },
                "result": {
                    "$ref": "#/definitions/ai.AnalysisResult"
                },
                "tokens_used": {
                    "$ref": "#/definitions/ai.TokenUsage"
                }
            }
        },
        "ai.AnalysisResult": {
            "type": "object",
            "properties": {
                "grading_details": {
                    "description": "详细说明得分点和扣分点",
                    "type": "string"
                },
                "score": {
                    "description": "计算得到的最终数字得分",
                    "type": "integer"
                },
                "student_answer": {
                    "description": "学生答案内容",
                    "type": "string"
                }
            }
        },
        "ai.BalanceInfo": {
            "type": "object",
            "properties": {
                "free_balance": {
                    "type": "integer"
                },
                "paid_balance": {
                    "type": "integer"
                },
                "total_balance": {
                    "type": "integer"
                }
            }
        },
        "ai.ConfigItem": {
            "type": "object",
            "properties": {
                "actions": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "ai.CostInfo": {
            "type": "object",
            "properties": {
                "completion_cost": {
                    "type": "number"
                },
                "points_consumed": {
                    "type": "integer"
                },
                "prompt_cost": {
                    "type": "number"
                },
                "total_cost": {
                    "type": "number"
                }
            }
        },
        "ai.ModelsUsed": {
            "type": "object",
            "properties": {
                "analysis_model": {
                    "type": "string"
                },
                "ocr_model": {
                    "type": "string"
                }
            }
        },
        "ai.Subject": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "key": {
                    "type": "string"
                },
                "text": {
                    "type": "string"
                }
            }
        },
        "ai.TokenUsage": {
            "type": "object",
            "properties": {
                "completion_tokens": {
                    "type": "integer"
                },
                "prompt_tokens": {
                    "type": "integer"
                },
                "total_tokens": {
                    "type": "integer"
                }
            }
        },
        "auth.AuthResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "integer"
                },
                "expires_in": {
                    "type": "integer"
                },
                "refresh_token": {
                    "type": "string"
                },
                "subjects": {
                    "description": "支持的科目列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/auth.Subject"
                    }
                },
                "user": {
                    "$ref": "#/definitions/auth.User"
                }
            }
        },
        "auth.CreateUserRequest": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                }
            }
        },
        "auth.EmailCodeRequest": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string"
                }
            }
        },
        "auth.EmailLoginRequest": {
            "type": "object",
            "required": [
                "code",
                "email"
            ],
            "properties": {
                "code": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                }
            }
        },
        "auth.LoginRequest": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                }
            }
        },
        "auth.RecoverRequest": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string"
                }
            }
        },
        "auth.RefreshTokenRequest": {
            "type": "object",
            "required": [
                "refresh_token"
            ],
            "properties": {
                "refresh_token": {
                    "type": "string"
                }
            }
        },
        "auth.RegisterRequest": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                }
            }
        },
        "auth.Subject": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "description",
                    "type": "string"
                },
                "key": {
                    "description": "prompt_key",
                    "type": "string"
                }
            }
        },
        "auth.UpdatePasswordRequest": {
            "type": "object",
            "required": [
                "password"
            ],
            "properties": {
                "password": {
                    "type": "string",
                    "minLength": 6
                }
            }
        },
        "auth.User": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "auth.UserListResponse": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "success": {
                    "type": "boolean"
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/auth.User"
                    }
                }
            }
        },
        "auth.VerifyRequest": {
            "type": "object",
            "required": [
                "email",
                "token",
                "type"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "config.AppConfig": {
            "type": "object",
            "properties": {
                "admin_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "download_url": {
                    "type": "string"
                },
                "force_update": {
                    "type": "boolean"
                },
                "update_log": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "finance.AddExpenseRequest": {
            "type": "object",
            "required": [
                "amount",
                "category",
                "description",
                "service_period_end",
                "service_period_start",
                "transaction_date",
                "vendor"
            ],
            "properties": {
                "amount": {
                    "description": "支付金额，单位：分",
                    "type": "integer",
                    "minimum": 1
                },
                "category": {
                    "description": "支出类别",
                    "type": "string"
                },
                "description": {
                    "description": "支出描述",
                    "type": "string"
                },
                "invoice_url": {
                    "description": "发票URL",
                    "type": "string"
                },
                "payment_method": {
                    "description": "支付方式",
                    "type": "string"
                },
                "service_period_end": {
                    "description": "服务周期结束，格式：YYYY-MM-DD",
                    "type": "string"
                },
                "service_period_start": {
                    "description": "服务周期开始，格式：YYYY-MM-DD",
                    "type": "string"
                },
                "transaction_date": {
                    "description": "实际支付日期，格式：YYYY-MM-DD",
                    "type": "string"
                },
                "vendor": {
                    "description": "供应商",
                    "type": "string"
                }
            }
        },
        "finance.CategoryAnalysis": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "金额（分）",
                    "type": "integer"
                },
                "count": {
                    "description": "记录数",
                    "type": "integer"
                },
                "percentage": {
                    "description": "占比",
                    "type": "number"
                }
            }
        },
        "finance.Expense": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "支付金额，单位：分",
                    "type": "integer"
                },
                "category": {
                    "description": "支出类别",
                    "allOf": [
                        {
                            "$ref": "#/definitions/finance.ExpenseCategory"
                        }
                    ]
                },
                "created_at": {
                    "type": "string"
                },
                "currency": {
                    "description": "货币种类",
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "invoice_url": {
                    "description": "发票URL",
                    "type": "string"
                },
                "payment_method": {
                    "description": "支付方式",
                    "type": "string"
                },
                "recorded_by": {
                    "description": "记录人",
                    "type": "string"
                },
                "service_period_end": {
                    "description": "服务周期结束",
                    "type": "string"
                },
                "service_period_start": {
                    "description": "服务周期开始",
                    "type": "string"
                },
                "transaction_date": {
                    "description": "实际支付日期",
                    "type": "string"
                },
                "vendor": {
                    "description": "供应商",
                    "type": "string"
                }
            }
        },
        "finance.ExpenseAnalysis": {
            "type": "object",
            "properties": {
                "category_breakdown": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/finance.CategoryAnalysis"
                    }
                },
                "monthly_trend": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/finance.MonthlyExpense"
                    }
                },
                "top_vendors": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/finance.VendorExpense"
                    }
                }
            }
        },
        "finance.ExpenseCategory": {
            "type": "string",
            "enum": [
                "infrastructure",
                "software",
                "marketing",
                "operational",
                "other"
            ],
            "x-enum-comments": {
                "CategoryInfrastructure": "基础设施",
                "CategoryMarketing": "市场营销",
                "CategoryOperational": "运营费用",
                "CategoryOther": "其他",
                "CategorySoftware": "软件服务"
            },
            "x-enum-descriptions": [
                "基础设施",
                "软件服务",
                "市场营销",
                "运营费用",
                "其他"
            ],
            "x-enum-varnames": [
                "CategoryInfrastructure",
                "CategorySoftware",
                "CategoryMarketing",
                "CategoryOperational",
                "CategoryOther"
            ]
        },
        "finance.ExpenseListResponse": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "expenses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/finance.Expense"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "finance.ExpenseReport": {
            "type": "object",
            "properties": {
                "expense_count": {
                    "description": "支出记录数",
                    "type": "integer"
                },
                "expenses_by_category": {
                    "description": "按类别分组的支出",
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer",
                        "format": "int64"
                    }
                },
                "total_expenses": {
                    "description": "总支出（分）",
                    "type": "integer"
                }
            }
        },
        "finance.FinancialReport": {
            "type": "object",
            "properties": {
                "expenses": {
                    "$ref": "#/definitions/finance.ExpenseReport"
                },
                "period": {
                    "$ref": "#/definitions/finance.ReportPeriod"
                },
                "profit": {
                    "$ref": "#/definitions/finance.ProfitReport"
                },
                "revenue": {
                    "$ref": "#/definitions/finance.RevenueReport"
                },
                "user_metrics": {
                    "$ref": "#/definitions/finance.UserMetricsReport"
                }
            }
        },
        "finance.FinancialReportRequest": {
            "type": "object",
            "required": [
                "end_date",
                "start_date"
            ],
            "properties": {
                "end_date": {
                    "description": "报表结束日期, 格式: YYYY-MM-DD",
                    "type": "string"
                },
                "start_date": {
                    "description": "报表开始日期, 格式: YYYY-MM-DD",
                    "type": "string"
                }
            }
        },
        "finance.MonthlyExpense": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "金额（分）",
                    "type": "integer"
                },
                "month": {
                    "description": "月份，格式：YYYY-MM",
                    "type": "string"
                }
            }
        },
        "finance.ProfitReport": {
            "type": "object",
            "properties": {
                "gross_profit": {
                    "description": "毛利润（分）",
                    "type": "integer"
                },
                "profit_margin": {
                    "description": "利润率",
                    "type": "number"
                }
            }
        },
        "finance.ReportPeriod": {
            "type": "object",
            "properties": {
                "end_date": {
                    "type": "string"
                },
                "start_date": {
                    "type": "string"
                }
            }
        },
        "finance.RevenueReport": {
            "type": "object",
            "properties": {
                "average_recharge": {
                    "description": "平均充值金额（分）",
                    "type": "integer"
                },
                "recharge_count": {
                    "description": "充值次数",
                    "type": "integer"
                },
                "total_revenue": {
                    "description": "总收入（分）",
                    "type": "integer"
                }
            }
        },
        "finance.UserMetricsReport": {
            "type": "object",
            "properties": {
                "active_users": {
                    "description": "活跃用户数",
                    "type": "integer"
                },
                "new_users": {
                    "description": "新用户数",
                    "type": "integer"
                },
                "paying_users": {
                    "description": "付费用户数",
                    "type": "integer"
                }
            }
        },
        "finance.VendorExpense": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "金额（分）",
                    "type": "integer"
                },
                "vendor": {
                    "description": "供应商名称",
                    "type": "string"
                }
            }
        },
        "models.SwaggerEmptyResponse": {
            "description": "无数据响应格式，通常用于删除或更新操作",
            "type": "object",
            "properties": {
                "message": {
                    "description": "成功消息",
                    "type": "string",
                    "example": "操作成功"
                },
                "success": {
                    "description": "请求成功标识",
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "models.SwaggerForbiddenErrorResponse": {
            "description": "权限不足错误响应格式",
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "string",
                    "example": "FORBIDDEN"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string",
                    "example": "需要管理员权限"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string",
                    "example": "权限不足"
                },
                "success": {
                    "description": "请求失败标识",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "models.SwaggerInsufficientBalanceErrorResponse": {
            "description": "余额不足错误响应格式",
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "string",
                    "example": "INSUFFICIENT_BALANCE"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string",
                    "example": "当前余额不足以完成此操作"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string",
                    "example": "余额不足"
                },
                "success": {
                    "description": "请求失败标识",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "models.SwaggerInternalServerErrorResponse": {
            "description": "服务器内部错误响应格式",
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "string",
                    "example": "INTERNAL_ERROR"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string",
                    "example": "数据库连接失败"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string",
                    "example": "服务器内部错误"
                },
                "success": {
                    "description": "请求失败标识",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "models.SwaggerNotFoundErrorResponse": {
            "description": "资源不存在错误响应格式",
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "string",
                    "example": "NOT_FOUND"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string",
                    "example": "用户不存在"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string",
                    "example": "资源不存在"
                },
                "success": {
                    "description": "请求失败标识",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "models.SwaggerServiceUnavailableErrorResponse": {
            "description": "服务不可用错误响应格式",
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "string",
                    "example": "SERVICE_UNAVAILABLE"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string",
                    "example": "AI服务暂时不可用"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string",
                    "example": "外部服务不可用"
                },
                "success": {
                    "description": "请求失败标识",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "models.SwaggerUnauthorizedErrorResponse": {
            "description": "未授权错误响应格式",
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "string",
                    "example": "UNAUTHORIZED"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string",
                    "example": "访问令牌无效或已过期"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string",
                    "example": "未授权访问"
                },
                "success": {
                    "description": "请求失败标识",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "models.SwaggerValidationErrorResponse": {
            "description": "参数验证错误响应格式",
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "string",
                    "example": "VALIDATION_ERROR"
                },
                "error": {
                    "description": "具体验证错误信息",
                    "type": "string",
                    "example": "email字段格式不正确"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string",
                    "example": "请求参数验证失败"
                },
                "success": {
                    "description": "请求失败标识",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "recharge.AdminProcessRequest": {
            "type": "object",
            "required": [
                "action",
                "request_id"
            ],
            "properties": {
                "action": {
                    "enum": [
                        "approved",
                        "rejected"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/recharge.ApprovalStatus"
                        }
                    ]
                },
                "admin_note": {
                    "type": "string"
                },
                "request_id": {
                    "type": "integer"
                },
                "rmb_amount": {
                    "description": "管理员可修改的实际充值金额",
                    "type": "number"
                }
            }
        },
        "recharge.ApprovalStatus": {
            "type": "string",
            "enum": [
                "pending",
                "approved",
                "rejected"
            ],
            "x-enum-varnames": [
                "StatusPending",
                "StatusApproved",
                "StatusRejected"
            ]
        },
        "recharge.RechargeListResponse": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "requests": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/recharge.RechargeRequest"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "recharge.RechargeRequest": {
            "type": "object",
            "properties": {
                "admin_note": {
                    "type": "string"
                },
                "amount": {
                    "description": "充值金额（人民币，单位：元）",
                    "type": "number"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "order_id": {
                    "type": "string"
                },
                "payment_method": {
                    "type": "string"
                },
                "payment_proof": {
                    "type": "string"
                },
                "points_to_grant": {
                    "description": "根据充值金额换算出的积分",
                    "type": "integer"
                },
                "processed_at": {
                    "type": "string"
                },
                "processed_by": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/recharge.ApprovalStatus"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "recharge.UserRechargeRequest": {
            "type": "object",
            "required": [
                "amount",
                "order_id",
                "payment_proof"
            ],
            "properties": {
                "amount": {
                    "description": "充值金额（人民币,单位：元）",
                    "type": "integer",
                    "minimum": 1
                },
                "order_id": {
                    "description": "支付宝订单号",
                    "type": "string"
                },
                "payment_proof": {
                    "description": "支付凭证URL",
                    "type": "string"
                }
            }
        },
        "response.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {},
                "error": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                }
            }
        },
        "toshandler.TOSCredentials": {
            "type": "object",
            "properties": {
                "access_key_id": {
                    "type": "string"
                },
                "expiration": {
                    "type": "string"
                },
                "secret_access_key": {
                    "type": "string"
                },
                "session_token": {
                    "type": "string"
                }
            }
        },
        "toshandler.TOSCredentialsResponse": {
            "type": "object",
            "properties": {
                "bucket": {
                    "type": "string"
                },
                "credentials": {
                    "$ref": "#/definitions/toshandler.TOSCredentials"
                },
                "endpoint": {
                    "type": "string"
                },
                "intranet_endpoint": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "region": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                }
            }
        },
        "wallet.AdjustBalanceRequest": {
            "type": "object",
            "required": [
                "amount",
                "user_id"
            ],
            "properties": {
                "amount": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "wallet.BalanceResponse": {
            "type": "object",
            "properties": {
                "free_balance": {
                    "type": "integer"
                },
                "paid_balance": {
                    "type": "integer"
                },
                "total_balance": {
                    "type": "integer"
                }
            }
        },
        "wallet.Transaction": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "free_balance_after": {
                    "type": "integer"
                },
                "free_points_change": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "metadata": {
                    "type": "string"
                },
                "paid_balance_after": {
                    "type": "integer"
                },
                "paid_points_change": {
                    "type": "integer"
                },
                "related_consumption_id": {
                    "type": "string"
                },
                "related_recharge_id": {
                    "type": "integer"
                },
                "source": {
                    "$ref": "#/definitions/wallet.TransactionSource"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "wallet.TransactionSource": {
            "type": "string",
            "enum": [
                "RECHARGE",
                "CONSUMPTION",
                "GIFT_SIGNUP",
                "GIFT_PROMO",
                "REFUND"
            ],
            "x-enum-comments": {
                "TransactionSourceConsumption": "业务消费",
                "TransactionSourceGiftPromo": "活动赠送",
                "TransactionSourceGiftSignup": "注册赠送",
                "TransactionSourceRecharge": "付费充值",
                "TransactionSourceRefund": "退款"
            },
            "x-enum-descriptions": [
                "付费充值",
                "业务消费",
                "注册赠送",
                "活动赠送",
                "退款"
            ],
            "x-enum-varnames": [
                "TransactionSourceRecharge",
                "TransactionSourceConsumption",
                "TransactionSourceGiftSignup",
                "TransactionSourceGiftPromo",
                "TransactionSourceRefund"
            ]
        },
        "wallet.TransactionsResponse": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "transactions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/wallet.Transaction"
                    }
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "X-Access-Token",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:9000",
	BasePath:         "/",
	Schemes:          []string{"http", "https"},
	Title:            "Serverless AIG API",
	Description:      "This is a serverless AI gateway API server with authentication, wallet management, and AI model integration.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
