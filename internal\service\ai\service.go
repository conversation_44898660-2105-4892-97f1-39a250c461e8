package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"math"

	"serverless-aig/internal/client/volcengine"
	"serverless-aig/internal/domain/ai"
	"serverless-aig/internal/domain/wallet"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/internal/repository/postgres"
	"serverless-aig/pkg/errors"

	walletservice "serverless-aig/internal/service/wallet"

	jsonrepair "github.com/RealAlexandreAI/json-repair"
	"github.com/google/uuid"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
)

// Service AI分析服务接口
type Service interface {
	Analysis(ctx context.Context, userID uuid.UUID, req *ai.AnalysisRequest) (*ai.AnalysisResponse, error)
	GetConfigs(ctx context.Context) ([]ai.ConfigItem, error)
	GetSubjects(ctx context.Context) ([]ai.Subject, error)
}

// service AI分析服务实现
type service struct {
	volcClient    *volcengine.Client
	walletService walletservice.Service
	aiRepo        interfaces.AIRepository
	logger        *logger.Logger
}

// NewService 创建AI分析服务
func NewService(volcClient *volcengine.Client, walletService walletservice.Service, db *postgres.DB, logger *logger.Logger) Service {
	return &service{
		volcClient:    volcClient,
		walletService: walletService,
		aiRepo:        postgres.NewAIRepository(db),
		logger:        logger,
	}
}

// Analysis AI分析
func (s *service) Analysis(ctx context.Context, userID uuid.UUID, req *ai.AnalysisRequest) (*ai.AnalysisResponse, error) {
	// 验证请求参数
	if err := s.validateAnalysisRequest(req); err != nil {
		return nil, err
	}

	// 获取系统提示词
	prompt, err := s.aiRepo.GetPrompt(ctx, req.PromptKey)
	if err != nil {
		s.logger.LogError("获取系统提示词失败: %v", err)
		return nil, errors.NewBusinessError("PROMPT_NOT_FOUND", "系统提示词不存在")
	}

	// 设置默认分析模式
	if req.AnalysisMode == "" {
		req.AnalysisMode = ai.StandardMode
	}

	// 根据模式执行分析
	result, tokenUsage, modelsUsed, err := s.performAnalysis(ctx, req, prompt.Text)
	if err != nil {
		s.logger.LogError("执行AI分析失败: %v", err)
		return nil, err
	}

	// 计算成本
	costInfo := s.calculateCost(tokenUsage, req.AnalysisMode)

	// 消费积分
	balance, err := s.consumePoints(ctx, userID, costInfo.PointsConsumed, req)
	if err != nil {
		s.logger.LogError("消费积分失败: %v", err)
		return nil, err
	}

	return &ai.AnalysisResponse{
		Result:     *result,
		TokensUsed: *tokenUsage,
		CostInfo:   *costInfo,
		Balance:    *balance,
		Mode:       req.AnalysisMode,
		Models:     *modelsUsed,
	}, nil
}

// validateAnalysisRequest 验证分析请求
func (s *service) validateAnalysisRequest(req *ai.AnalysisRequest) error {
	if req.Text == "" {
		return errors.NewValidationError("text", "评分标准不能为空")
	}

	if req.PromptKey == "" {
		return errors.NewValidationError("prompt_key", "系统提示词键值不能为空")
	}

	if req.Content == "" {
		return errors.NewValidationError("content", "学生答案图片URL不能为空")
	}

	if req.AnalysisMode != "" && !ai.IsValidMode(req.AnalysisMode) {
		return errors.NewValidationError("analysis_mode", "无效的分析模式")
	}

	return nil
}

// performAnalysis 执行分析
func (s *service) performAnalysis(ctx context.Context, req *ai.AnalysisRequest, systemPrompt string) (*ai.AnalysisResult, *ai.TokenUsage, *ai.ModelsUsed, error) {
	ocrModel, analysisModel := ai.GetModeModels(req.AnalysisMode)

	var result *ai.AnalysisResult
	var totalTokenUsage ai.TokenUsage
	var err error

	if req.AnalysisMode == ai.TraditionalMode {
		// 传统模式：先OCR，再分析
		result, totalTokenUsage, err = s.performTraditionalAnalysis(ctx, req, systemPrompt, ocrModel, analysisModel)
	} else {
		// 其他模式：直接多模态分析
		result, totalTokenUsage, err = s.performDirectAnalysis(ctx, req, systemPrompt, analysisModel)
	}

	if err != nil {
		return nil, nil, nil, err
	}

	modelsUsed := &ai.ModelsUsed{
		OCRModel:      ocrModel,
		AnalysisModel: analysisModel,
	}

	return result, &totalTokenUsage, modelsUsed, nil
}

// performTraditionalAnalysis 执行传统模式分析
func (s *service) performTraditionalAnalysis(ctx context.Context, req *ai.AnalysisRequest, systemPrompt, ocrModel, analysisModel string) (*ai.AnalysisResult, ai.TokenUsage, error) {
	// OCR提示词
	ocrUserPrompt := `Role:教育专用手写OCR引擎
Action:准确识别图片中学生手写答题内容
Constraint:仅识别中文和英文,忽略划掉的文本,数学公式用LaTeX格式,如 (E=mc^2)
Input:用户上传的学生答题图片
Ouput: 直接输出识别结果，不要添加任何解释。`
	// 第一步：OCR识别
	ocrMessages := []model.ChatCompletionMessage{
		volcengine.CreateImageMessage(model.ChatMessageRoleUser, ocrUserPrompt, req.Content),
	}

	ocrRequest := s.volcClient.CreateChatCompletionRequest(ocrModel, ocrMessages,
		volcengine.WithMaxTokens(ai.MaxTokens),
		volcengine.WithTemperature(0.3))

	ocrResp, err := s.volcClient.ChatCompletion(ctx, ocrRequest)
	if err != nil {
		return nil, ai.TokenUsage{}, errors.NewExternalServiceError("volcengine", "OCR识别失败", err)
	}

	// 从OCR响应中提取文本
	studentAnswer := ""
	if len(ocrResp.Choices) > 0 && ocrResp.Choices[0].Message.Content != nil {
		if ocrResp.Choices[0].Message.Content.StringValue != nil {
			studentAnswer = *ocrResp.Choices[0].Message.Content.StringValue
		}
	}

	// 第二步：文本分析
	analysisPrompt := fmt.Sprintf(`%s

评分标准：
%s

学生答案：
%s

请严格按照以下JSON格式输出结果：
{
  "student_answer": "学生答案内容",
  "score": 分数(整数),
  "grading_details": "详细的评分说明"
}`, systemPrompt, req.Text, studentAnswer)

	analysisMessages := []model.ChatCompletionMessage{
		volcengine.CreateMessage("user", analysisPrompt),
	}

	analysisRequest := s.volcClient.CreateChatCompletionRequest(analysisModel, analysisMessages,
		volcengine.WithMaxTokens(ai.MaxTokens),
		volcengine.WithTemperature(0.1))

	analysisResp, err := s.volcClient.ChatCompletion(ctx, analysisRequest)
	if err != nil {
		return nil, ai.TokenUsage{}, errors.NewExternalServiceError("volcengine", "文本分析失败", err)
	}

	// 解析分析结果
	// 从分析响应中提取内容
	analysisContent := ""
	if len(analysisResp.Choices) > 0 && analysisResp.Choices[0].Message.Content != nil {
		if analysisResp.Choices[0].Message.Content.StringValue != nil {
			analysisContent = *analysisResp.Choices[0].Message.Content.StringValue
		}
	}

	result, err := s.parseAnalysisResult(analysisContent, studentAnswer)
	if err != nil {
		return nil, ai.TokenUsage{}, err
	}

	// 合并令牌使用情况
	totalTokenUsage := ai.TokenUsage{
		PromptTokens:     ocrResp.Usage.PromptTokens + analysisResp.Usage.PromptTokens,
		CompletionTokens: ocrResp.Usage.CompletionTokens + analysisResp.Usage.CompletionTokens,
		TotalTokens:      ocrResp.Usage.TotalTokens + analysisResp.Usage.TotalTokens,
	}

	return result, totalTokenUsage, nil
}

// performDirectAnalysis 执行直接多模态分析
func (s *service) performDirectAnalysis(ctx context.Context, req *ai.AnalysisRequest, systemPrompt, analysisModel string) (*ai.AnalysisResult, ai.TokenUsage, error) {
	userPrompt := fmt.Sprintf(`评分标准：
%s

请分析图片中的学生答案，并严格按照以下JSON格式输出结果：
{
  "student_answer": "从图片中提取的学生答案内容",
  "score": 分数(整数),
  "grading_details": "详细的评分说明"
}`, req.Text)

	messages := []model.ChatCompletionMessage{
		volcengine.CreateMessage(model.ChatMessageRoleSystem, systemPrompt),
		volcengine.CreateImageMessage(model.ChatMessageRoleUser, userPrompt, req.Content),
	}

	// 为标准模式和经济模式创建响应格式
	var requestOptions []volcengine.RequestOption
	if req.AnalysisMode == ai.StandardMode || req.AnalysisMode == ai.EconomyMode {
		responseFormat := s.createStandardAnalysisResponseFormat()
		requestOptions = append(requestOptions, volcengine.WithResponseFormat(responseFormat))
	}

	requestOptions = append(requestOptions,
		volcengine.WithMaxTokens(ai.MaxTokens),
		volcengine.WithTemperature(0.1))

	request := s.volcClient.CreateChatCompletionRequest(analysisModel, messages, requestOptions...)

	resp, err := s.volcClient.ChatCompletion(ctx, request)
	if err != nil {
		return nil, ai.TokenUsage{}, errors.NewExternalServiceError("volcengine", "多模态分析失败", err)
	}

	// 解析分析结果
	// 从响应中提取内容
	responseContent := ""
	if len(resp.Choices) > 0 && resp.Choices[0].Message.Content != nil {
		if resp.Choices[0].Message.Content.StringValue != nil {
			responseContent = *resp.Choices[0].Message.Content.StringValue
		}
	}

	result, err := s.parseAnalysisResult(responseContent, "")
	if err != nil {
		return nil, ai.TokenUsage{}, err
	}

	tokenUsage := ai.TokenUsage{
		PromptTokens:     resp.Usage.PromptTokens,
		CompletionTokens: resp.Usage.CompletionTokens,
		TotalTokens:      resp.Usage.TotalTokens,
	}

	return result, tokenUsage, nil
}

// parseAnalysisResult 解析分析结果
func (s *service) parseAnalysisResult(content, fallbackStudentAnswer string) (*ai.AnalysisResult, error) {
	// 尝试修复JSON
	repairedJSON, err := jsonrepair.RepairJSON(content)
	if err != nil {
		s.logger.LogError("JSON修复失败: %v", err)
		return nil, errors.NewBusinessError("PARSE_ERROR", "分析结果解析失败")
	}

	var result ai.AnalysisResult
	err = json.Unmarshal([]byte(repairedJSON), &result)
	if err != nil {
		s.logger.LogError("JSON解析失败: %v", err)
		return nil, errors.NewBusinessError("PARSE_ERROR", "分析结果解析失败")
	}

	// 如果没有学生答案且有fallback，使用fallback
	if result.StudentAnswer == "" && fallbackStudentAnswer != "" {
		result.StudentAnswer = fallbackStudentAnswer
	}

	return &result, nil
}

// createStandardAnalysisResponseFormat 创建标准分析响应格式
func (s *service) createStandardAnalysisResponseFormat() *model.ResponseFormat {
	schema := map[string]any{
		"type": "object",
		"properties": map[string]any{
			"student_answer": map[string]any{
				"type":        "string",
				"description": "从图片中提取学生答案内容",
			},
			"score": map[string]any{
				"type":        "integer",
				"description": "计算得到的最终数字得分",
			},
			"grading_details": map[string]any{
				"type":        "string",
				"description": "根据评分标准，详细说明得分点的获取情况和扣分点的具体原因，需清晰对应学生作答内容",
			},
		},
		"required": []string{"student_answer", "score", "grading_details"},
	}

	return volcengine.CreateResponseFormat("json_schema", schema)
}

// calculateCost 计算成本
func (s *service) calculateCost(tokenUsage *ai.TokenUsage, mode ai.AnalysisMode) *ai.CostInfo {
	pricing := ai.DefaultPricing

	promptCost := float64(tokenUsage.PromptTokens) * pricing.PromptPrice
	completionCost := float64(tokenUsage.CompletionTokens) * pricing.CompletionPrice
	totalCost := promptCost + completionCost

	// TODO：临时处理，后续根据实际需求调整
	// 应用成本倍率
	totalCost *= ai.DefaultCostMultiplier

	// 转换为积分（1分 = 1积分）
	pointsConsumed := int64(math.Ceil(totalCost * 100))

	return &ai.CostInfo{
		PromptCost:     promptCost,
		CompletionCost: completionCost,
		TotalCost:      totalCost,
		PointsConsumed: pointsConsumed,
	}
}

// consumePoints 消费积分
func (s *service) consumePoints(ctx context.Context, userID uuid.UUID, points int64, req *ai.AnalysisRequest) (*ai.BalanceInfo, error) {
	// 生成消费ID
	consumptionID := fmt.Sprintf("ai_analysis_%s_%d", req.AnalysisMode, points)

	// 消费积分
	consumeReq := &wallet.ConsumePointsRequest{
		UserID:               userID,
		Amount:               points,
		Description:          fmt.Sprintf("AI分析消费 - %s模式", req.AnalysisMode),
		RelatedConsumptionID: consumptionID,
	}

	updatedWallet, err := s.walletService.ConsumePoints(ctx, consumeReq)
	if err != nil {
		return nil, err
	}

	return &ai.BalanceInfo{
		PaidBalance:  updatedWallet.PaidBalance,
		FreeBalance:  updatedWallet.FreeBalance,
		TotalBalance: updatedWallet.TotalBalance(),
	}, nil
}

// GetConfigs 获取配置列表
func (s *service) GetConfigs(ctx context.Context) ([]ai.ConfigItem, error) {
	configs, err := s.aiRepo.GetConfigs(ctx)
	if err != nil {
		s.logger.LogError("获取配置列表失败: %v", err)
		return nil, err
	}

	result := make([]ai.ConfigItem, len(configs))
	for i, config := range configs {
		result[i] = ai.ConfigItem{
			ID:      config.ID,
			Name:    config.Name,
			URL:     config.URL,
			Actions: config.Actions,
		}
	}

	return result, nil
}

// GetSubjects 获取学科列表
func (s *service) GetSubjects(ctx context.Context) ([]ai.Subject, error) {
	prompts, err := s.aiRepo.GetPrompts(ctx)
	if err != nil {
		s.logger.LogError("获取学科列表失败: %v", err)
		return nil, err
	}

	result := make([]ai.Subject, len(prompts))
	for i, prompt := range prompts {
		result[i] = ai.Subject{
			Key:         prompt.Key,
			Text:        prompt.Text,
			Description: prompt.Description,
			Category:    prompt.Category,
		}
	}

	return result, nil
}
