package finance

import (
	"time"

	"github.com/google/uuid"
)

// ExpenseCategory 支出类别枚举
type ExpenseCategory string

const (
	CategoryInfrastructure ExpenseCategory = "infrastructure" // 基础设施
	CategorySoftware       ExpenseCategory = "software"       // 软件服务
	CategoryMarketing      ExpenseCategory = "marketing"      // 市场营销
	CategoryOperational    ExpenseCategory = "operational"    // 运营费用
	CategoryOther          ExpenseCategory = "other"          // 其他
)

// Expense 支出记录领域模型
type Expense struct {
	ID                 int64           `json:"id" db:"id"`
	Description        string          `json:"description" db:"description"`
	Amount             int64           `json:"amount" db:"amount"`                             // 支付金额，单位：分
	Currency           string          `json:"currency" db:"currency"`                         // 货币种类
	Category           ExpenseCategory `json:"category" db:"category"`                         // 支出类别
	Vendor             string          `json:"vendor" db:"vendor"`                             // 供应商
	TransactionDate    time.Time       `json:"transaction_date" db:"transaction_date"`         // 实际支付日期
	ServicePeriodStart time.Time       `json:"service_period_start" db:"service_period_start"` // 服务周期开始
	ServicePeriodEnd   time.Time       `json:"service_period_end" db:"service_period_end"`     // 服务周期结束
	PaymentMethod      string          `json:"payment_method" db:"payment_method"`             // 支付方式
	InvoiceURL         string          `json:"invoice_url" db:"invoice_url"`                   // 发票URL
	RecordedBy         *uuid.UUID      `json:"recorded_by" db:"recorded_by"`                   // 记录人
	CreatedAt          time.Time       `json:"created_at" db:"created_at"`
}

// AddExpenseRequest 新增支出请求
type AddExpenseRequest struct {
	Description        string `json:"description" binding:"required"`          // 支出描述
	Amount             int64  `json:"amount" binding:"required,min=1"`         // 支付金额，单位：分
	Category           string `json:"category" binding:"required"`             // 支出类别
	Vendor             string `json:"vendor" binding:"required"`               // 供应商
	TransactionDate    string `json:"transaction_date" binding:"required"`     // 实际支付日期，格式：YYYY-MM-DD
	ServicePeriodStart string `json:"service_period_start" binding:"required"` // 服务周期开始，格式：YYYY-MM-DD
	ServicePeriodEnd   string `json:"service_period_end" binding:"required"`   // 服务周期结束，格式：YYYY-MM-DD
	PaymentMethod      string `json:"payment_method"`                          // 支付方式
	InvoiceURL         string `json:"invoice_url"`                             // 发票URL
}

// FinancialReportRequest 财务报表请求
type FinancialReportRequest struct {
	StartDate string `json:"start_date" binding:"required"` // 报表开始日期, 格式: YYYY-MM-DD
	EndDate   string `json:"end_date" binding:"required"`   // 报表结束日期, 格式: YYYY-MM-DD
}

// FinancialReport 财务报表
type FinancialReport struct {
	Period      ReportPeriod      `json:"period"`
	Revenue     RevenueReport     `json:"revenue"`
	Expenses    ExpenseReport     `json:"expenses"`
	Profit      ProfitReport      `json:"profit"`
	UserMetrics UserMetricsReport `json:"user_metrics"`
}

// ReportPeriod 报表周期
type ReportPeriod struct {
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
}

// RevenueReport 收入报表
type RevenueReport struct {
	TotalRevenue    int64 `json:"total_revenue"`    // 总收入（分）
	RechargeCount   int   `json:"recharge_count"`   // 充值次数
	AverageRecharge int64 `json:"average_recharge"` // 平均充值金额（分）
}

// ExpenseReport 支出报表
type ExpenseReport struct {
	TotalExpenses      int64                     `json:"total_expenses"`       // 总支出（分）
	ExpensesByCategory map[ExpenseCategory]int64 `json:"expenses_by_category"` // 按类别分组的支出
	ExpenseCount       int                       `json:"expense_count"`        // 支出记录数
}

// ProfitReport 利润报表
type ProfitReport struct {
	GrossProfit  int64   `json:"gross_profit"`  // 毛利润（分）
	ProfitMargin float64 `json:"profit_margin"` // 利润率
}

// UserMetricsReport 用户指标报表
type UserMetricsReport struct {
	NewUsers    int `json:"new_users"`    // 新用户数
	ActiveUsers int `json:"active_users"` // 活跃用户数
	PayingUsers int `json:"paying_users"` // 付费用户数
}

// ExpenseAnalysis 支出分析
type ExpenseAnalysis struct {
	CategoryBreakdown map[ExpenseCategory]CategoryAnalysis `json:"category_breakdown"`
	MonthlyTrend      []MonthlyExpense                     `json:"monthly_trend"`
	TopVendors        []VendorExpense                      `json:"top_vendors"`
}

// CategoryAnalysis 类别分析
type CategoryAnalysis struct {
	Amount     int64   `json:"amount"`     // 金额（分）
	Count      int     `json:"count"`      // 记录数
	Percentage float64 `json:"percentage"` // 占比
}

// MonthlyExpense 月度支出
type MonthlyExpense struct {
	Month  string `json:"month"`  // 月份，格式：YYYY-MM
	Amount int64  `json:"amount"` // 金额（分）
}

// VendorExpense 供应商支出
type VendorExpense struct {
	Vendor string `json:"vendor"` // 供应商名称
	Amount int64  `json:"amount"` // 金额（分）
}

// ExpenseListRequest 支出列表请求
type ExpenseListRequest struct {
	Category  string `form:"category"`
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
	Limit     int    `form:"limit"`
	Offset    int    `form:"offset"`
}

// ExpenseListResponse 支出列表响应
type ExpenseListResponse struct {
	Expenses []Expense `json:"expenses"`
	Count    int       `json:"count"`
	Total    int       `json:"total"`
}
