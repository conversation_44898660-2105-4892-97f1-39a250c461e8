package volcengine

import (
	"context"
	"errors"
	"sync"

	"serverless-aig/internal/config"

	"github.com/volcengine/volcengine-go-sdk/service/arkruntime"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
)

// Client 火山引擎客户端
type Client struct {
	client *arkruntime.Client
	mu     sync.RWMutex
}

// New 创建新的火山引擎客户端
func New(cfg config.VolcengineConfig) *Client {
	if cfg.APIKey == "" {
		panic("火山引擎API密钥不能为空")
	}

	client := arkruntime.NewClientWithApiKey(cfg.APIKey)

	return &Client{
		client: client,
	}
}

// ChatCompletion 聊天完成
func (c *Client) ChatCompletion(ctx context.Context, req *model.CreateChatCompletionRequest) (*model.ChatCompletionResponse, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if c.client == nil {
		return nil, errors.New("火山引擎客户端未初始化")
	}

	resp, err := c.client.CreateChatCompletion(ctx, req)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

// CreateChatCompletionRequest 创建聊天完成请求
func (c *Client) CreateChatCompletionRequest(modelName string, messages []model.ChatCompletionMessage, options ...RequestOption) *model.CreateChatCompletionRequest {
	// 转换消息类型
	messagePointers := make([]*model.ChatCompletionMessage, len(messages))
	for i := range messages {
		messagePointers[i] = &messages[i]
	}

	req := &model.CreateChatCompletionRequest{
		Model:    modelName,
		Messages: messagePointers,
	}

	// 应用选项
	for _, option := range options {
		option(req)
	}

	return req
}

// RequestOption 请求选项
type RequestOption func(*model.CreateChatCompletionRequest)

// WithMaxTokens 设置最大令牌数
func WithMaxTokens(maxTokens int) RequestOption {
	return func(req *model.CreateChatCompletionRequest) {
		req.MaxTokens = &maxTokens
	}
}

// WithTemperature 设置温度
func WithTemperature(temperature float64) RequestOption {
	return func(req *model.CreateChatCompletionRequest) {
		temp := float32(temperature)
		req.Temperature = &temp
	}
}

// WithResponseFormat 设置响应格式
func WithResponseFormat(format *model.ResponseFormat) RequestOption {
	return func(req *model.CreateChatCompletionRequest) {
		req.ResponseFormat = format
	}
}

// WithStream 设置流式响应
func WithStream(stream bool) RequestOption {
	return func(req *model.CreateChatCompletionRequest) {
		req.Stream = &stream
	}
}

// CreateMessage 创建消息
func CreateMessage(role, content string) model.ChatCompletionMessage {
	return model.ChatCompletionMessage{
		Role: role,
		Content: &model.ChatCompletionMessageContent{
			StringValue: &content,
		},
	}
}

// CreateImageMessage 创建图片消息
func CreateImageMessage(role string, textContent string, imageURL string) model.ChatCompletionMessage {
	textContentParts := []*model.ChatCompletionMessageContentPart{
		{
			Type: model.ChatCompletionMessageContentPartTypeImageURL,
			ImageURL: &model.ChatMessageImageURL{
				URL:    imageURL,
				Detail: model.ImageURLDetailHigh,
			},
		},
		{
			Type: model.ChatCompletionMessageContentPartTypeText,
			Text: textContent,
		},
	}
	return model.ChatCompletionMessage{
		Role: role,
		Content: &model.ChatCompletionMessageContent{
			ListValue: textContentParts,
		},
	}
}

// CreateResponseFormat 创建响应格式
func CreateResponseFormat(formatType string, schema map[string]any) *model.ResponseFormat {
	// JSONSchemaMarshaler 实现 json.Marshaler 接口的 JSON Schema 包装器
	type JSONSchemaMarshaler struct {
		Schema map[string]any
	}

	return &model.ResponseFormat{
		Type: model.ResponseFormatJSONSchema,
		JSONSchema: &model.ResponseFormatJSONSchemaJSONSchemaParam{
			Name:   "analysis_result",
			Schema: JSONSchemaMarshaler{Schema: schema},
			Strict: true,
		},
	}
}

// Reset 重置客户端
func (c *Client) Reset(cfg config.VolcengineConfig) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if cfg.APIKey == "" {
		return
	}

	c.client = arkruntime.NewClientWithApiKey(cfg.APIKey)
}
