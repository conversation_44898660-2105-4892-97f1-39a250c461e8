package postgres

import (
	"context"
	"database/sql"
	"serverless-aig/internal/domain/wallet"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/pkg/errors"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
)

// walletRepository 钱包仓储实现
type walletRepository struct {
	db *DB
}

// NewWalletRepository 创建钱包仓储
func NewWalletRepository(db *DB) interfaces.WalletRepository {
	return &walletRepository{db: db}
}

// GetWallet 获取钱包信息
func (r *walletRepository) GetWallet(ctx context.Context, userID uuid.UUID) (*wallet.Wallet, error) {
	query := `SELECT id, user_id, paid_balance, free_balance, created_at, updated_at 
			  FROM wallets WHERE user_id = $1`

	var w wallet.Wallet
	err := r.db.conn.GetContext(ctx, &w, query, userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.ErrUserNotFound
		}
		return nil, errors.NewDatabaseError("GetWallet", "获取钱包信息失败", err)
	}

	return &w, nil
}

// CreateWallet 创建钱包
func (r *walletRepository) CreateWallet(ctx context.Context, userID uuid.UUID) (*wallet.Wallet, error) {
	query := `INSERT INTO wallets (user_id, paid_balance, free_balance) 
			  VALUES ($1, 0, 500) 
			  RETURNING id, user_id, paid_balance, free_balance, created_at, updated_at`

	var w wallet.Wallet
	err := r.db.conn.GetContext(ctx, &w, query, userID)
	if err != nil {
		return nil, errors.NewDatabaseError("CreateWallet", "创建钱包失败", err)
	}

	// 创建注册奖励交易记录
	tx := &wallet.Transaction{
		UserID:               userID,
		Source:               wallet.TransactionSourceGiftSignup,
		PaidPointsChange:     0,
		FreePointsChange:     500,
		PaidBalanceAfter:     0,
		FreeBalanceAfter:     500,
		Description:          stringPtr("注册奖励"),
		RelatedRechargeID:    nil,
		RelatedConsumptionID: nil,
		Metadata:             nil,
	}

	if err := r.CreateTransaction(ctx, tx); err != nil {
		r.db.logger.LogError("创建注册奖励交易记录失败: %v", err)
	}

	return &w, nil
}

// UpdateWallet 更新钱包
func (r *walletRepository) UpdateWallet(ctx context.Context, w *wallet.Wallet) error {
	query := `UPDATE wallets 
			  SET paid_balance = $2, free_balance = $3, updated_at = NOW() 
			  WHERE user_id = $1`

	_, err := r.db.conn.ExecContext(ctx, query, w.UserID, w.PaidBalance, w.FreeBalance)
	if err != nil {
		return errors.NewDatabaseError("UpdateWallet", "更新钱包失败", err)
	}

	return nil
}

// CreateTransaction 创建交易记录
func (r *walletRepository) CreateTransaction(ctx context.Context, tx *wallet.Transaction) error {
	query := `INSERT INTO transactions 
			  (user_id, source, paid_points_change, free_points_change, 
			   paid_balance_after, free_balance_after, description, 
			   related_recharge_id, related_consumption_id, metadata) 
			  VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`

	_, err := r.db.conn.ExecContext(ctx, query,
		tx.UserID, tx.Source, tx.PaidPointsChange, tx.FreePointsChange,
		tx.PaidBalanceAfter, tx.FreeBalanceAfter, tx.Description,
		tx.RelatedRechargeID, tx.RelatedConsumptionID, tx.Metadata)
	if err != nil {
		return errors.NewDatabaseError("CreateTransaction", "创建交易记录失败", err)
	}

	return nil
}

// GetTransactions 获取交易记录
func (r *walletRepository) GetTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]wallet.Transaction, int, error) {
	// 获取总数
	countQuery := `SELECT COUNT(*) FROM transactions WHERE user_id = $1`
	var total int
	err := r.db.conn.GetContext(ctx, &total, countQuery, userID)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetTransactions", "获取交易记录总数失败", err)
	}

	// 获取交易记录
	query := `SELECT id, user_id, source, paid_points_change, free_points_change,
			  paid_balance_after, free_balance_after, description,
			  related_recharge_id, related_consumption_id, metadata, created_at
			  FROM transactions WHERE user_id = $1 
			  ORDER BY created_at DESC LIMIT $2 OFFSET $3`

	var transactions []wallet.Transaction
	err = r.db.conn.SelectContext(ctx, &transactions, query, userID, limit, offset)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetTransactions", "获取交易记录失败", err)
	}

	return transactions, total, nil
}

// GetTransactionsBySource 按来源获取交易记录
func (r *walletRepository) GetTransactionsBySource(ctx context.Context, userID uuid.UUID, source wallet.TransactionSource, limit, offset int) ([]wallet.Transaction, int, error) {
	// 获取总数
	countQuery := `SELECT COUNT(*) FROM transactions WHERE user_id = $1 AND source = $2`
	var total int
	err := r.db.conn.GetContext(ctx, &total, countQuery, userID, source)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetTransactionsBySource", "获取交易记录总数失败", err)
	}

	// 获取交易记录
	query := `SELECT id, user_id, source, paid_points_change, free_points_change,
			  paid_balance_after, free_balance_after, description,
			  related_recharge_id, related_consumption_id, metadata, created_at
			  FROM transactions WHERE user_id = $1 AND source = $2
			  ORDER BY created_at DESC LIMIT $3 OFFSET $4`

	var transactions []wallet.Transaction
	err = r.db.conn.SelectContext(ctx, &transactions, query, userID, source, limit, offset)
	if err != nil {
		return nil, 0, errors.NewDatabaseError("GetTransactionsBySource", "获取交易记录失败", err)
	}

	return transactions, total, nil
}

// ConsumePoints 消费积分
func (r *walletRepository) ConsumePoints(ctx context.Context, req *wallet.ConsumePointsRequest) (*wallet.Wallet, error) {
	var result wallet.Wallet

	err := r.db.WithTx(func(tx *sqlx.Tx) error {
		// 使用CTE原子性地消费积分
		query := `
		WITH wallet_update AS (
			UPDATE wallets 
			SET 
				free_balance = CASE 
					WHEN free_balance >= $2 THEN free_balance - $2
					ELSE 0
				END,
				paid_balance = CASE 
					WHEN free_balance >= $2 THEN paid_balance
					ELSE paid_balance - ($2 - free_balance)
				END,
				updated_at = NOW()
			WHERE user_id = $1 
			AND (paid_balance + free_balance) >= $2
			RETURNING id, user_id, paid_balance, free_balance, created_at, updated_at,
					  CASE WHEN free_balance >= $2 THEN 0 ELSE $2 - free_balance END as paid_consumed,
					  CASE WHEN free_balance >= $2 THEN $2 ELSE free_balance END as free_consumed
		),
		transaction_insert AS (
			INSERT INTO transactions 
			(user_id, source, paid_points_change, free_points_change, 
			 paid_balance_after, free_balance_after, description, related_consumption_id)
			SELECT user_id, 'CONSUMPTION', -paid_consumed, -free_consumed,
				   paid_balance, free_balance, $3, $4
			FROM wallet_update
			RETURNING id
		)
		SELECT id, user_id, paid_balance, free_balance, created_at, updated_at
		FROM wallet_update`

		err := tx.GetContext(ctx, &result, query, req.UserID, req.Amount, req.Description, req.RelatedConsumptionID)
		if err != nil {
			if err == sql.ErrNoRows {
				return errors.ErrInsufficientBalance
			}
			return err
		}

		return nil
	})
	if err != nil {
		return nil, errors.NewDatabaseError("ConsumePoints", "消费积分失败", err)
	}

	return &result, nil
}

// AdjustBalance 调整余额（仅用于添加免费积分）
func (r *walletRepository) AdjustBalance(ctx context.Context, userID uuid.UUID, amount int64, description string) (*wallet.Wallet, error) {
	var result wallet.Wallet

	err := r.db.WithTx(func(tx *sqlx.Tx) error {
		// 使用CTE原子性地调整余额
		query := `
		WITH wallet_update AS (
			UPDATE wallets 
			SET free_balance = free_balance + $2, updated_at = NOW()
			WHERE user_id = $1
			RETURNING id, user_id, paid_balance, free_balance, created_at, updated_at
		),
		transaction_insert AS (
			INSERT INTO transactions 
			(user_id, source, paid_points_change, free_points_change, 
			 paid_balance_after, free_balance_after, description)
			SELECT user_id, 'GIFT_PROMO', 0, $2,
				   paid_balance, free_balance, $3
			FROM wallet_update
			RETURNING id
		)
		SELECT id, user_id, paid_balance, free_balance, created_at, updated_at
		FROM wallet_update`

		err := tx.GetContext(ctx, &result, query, userID, amount, description)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, errors.NewDatabaseError("AdjustBalance", "调整余额失败", err)
	}

	return &result, nil
}

// AddPaidBalance 增加付费余额
func (r *walletRepository) AddPaidBalance(ctx context.Context, userID uuid.UUID, amount int64, description string, relatedRechargeID *int) (*wallet.Wallet, error) {
	var result wallet.Wallet

	err := r.db.WithTx(func(tx *sqlx.Tx) error {
		// 使用CTE原子性地增加付费余额
		query := `
		WITH wallet_update AS (
			UPDATE wallets 
			SET paid_balance = paid_balance + $2, updated_at = NOW()
			WHERE user_id = $1
			RETURNING id, user_id, paid_balance, free_balance, created_at, updated_at
		),
		transaction_insert AS (
			INSERT INTO transactions 
			(user_id, source, paid_points_change, free_points_change, 
			 paid_balance_after, free_balance_after, description, related_recharge_id)
			SELECT user_id, 'RECHARGE', $2, 0,
				   paid_balance, free_balance, $3, $4
			FROM wallet_update
			RETURNING id
		)
		SELECT id, user_id, paid_balance, free_balance, created_at, updated_at
		FROM wallet_update`

		err := tx.GetContext(ctx, &result, query, userID, amount, description, relatedRechargeID)
		if err != nil {
			if err == sql.ErrNoRows {
				return errors.ErrUserNotFound
			}
			return err
		}

		return nil
	})
	if err != nil {
		return nil, errors.NewDatabaseError("AddPaidBalance", "增加付费余额失败", err)
	}

	return &result, nil
}

// GetTotalBalance 获取总余额
func (r *walletRepository) GetTotalBalance(ctx context.Context, userID uuid.UUID) (int64, error) {
	query := `SELECT (paid_balance + free_balance) as total_balance FROM wallets WHERE user_id = $1`

	var totalBalance int64
	err := r.db.conn.GetContext(ctx, &totalBalance, query, userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, errors.ErrUserNotFound
		}
		return 0, errors.NewDatabaseError("GetTotalBalance", "获取总余额失败", err)
	}

	return totalBalance, nil
}

// GetUserCount 获取用户总数
func (r *walletRepository) GetUserCount(ctx context.Context) (int, error) {
	query := `SELECT COUNT(*) FROM wallets`

	var count int
	err := r.db.conn.GetContext(ctx, &count, query)
	if err != nil {
		return 0, errors.NewDatabaseError("GetUserCount", "获取用户总数失败", err)
	}

	return count, nil
}

// stringPtr 返回字符串指针
func stringPtr(s string) *string {
	return &s
}
