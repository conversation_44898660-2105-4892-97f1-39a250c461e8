package interfaces

import (
	"context"
	"serverless-aig/internal/domain/recharge"

	"github.com/google/uuid"
)

// RechargeRepository 充值仓储接口
type RechargeRepository interface {
	// 充值申请操作
	CreateRechargeRequest(ctx context.Context, req *recharge.RechargeRequest) error
	GetRechargeRequest(ctx context.Context, id int) (*recharge.RechargeRequest, error)
	GetRechargeRequestForUpdate(ctx context.Context, id int) (*recharge.RechargeRequest, error)
	GetRechargeRequestByOrderID(ctx context.Context, orderID string) (*recharge.RechargeRequest, error)
	UpdateRechargeRequest(ctx context.Context, req *recharge.RechargeRequest) error

	// 查询操作
	GetUserRechargeRequests(ctx context.Context, userID uuid.UUID, limit, offset int) ([]recharge.RechargeRequest, int, error)
	GetAdminRechargeRequests(ctx context.Context, status recharge.ApprovalStatus, limit, offset int) ([]recharge.RechargeRequest, int, error)
	GetAllRechargeRequests(ctx context.Context, limit, offset int) ([]recharge.RechargeRequest, int, error)

	// 统计操作
	GetRechargeStats(ctx context.Context, startDate, endDate string) (*RechargeStats, error)
}

// RechargeStats 充值统计
type RechargeStats struct {
	TotalAmount   float64 `json:"total_amount"`   // 总充值金额
	TotalCount    int     `json:"total_count"`    // 总充值次数
	AverageAmount float64 `json:"average_amount"` // 平均充值金额
	ApprovedCount int     `json:"approved_count"` // 已批准次数
	PendingCount  int     `json:"pending_count"`  // 待处理次数
	RejectedCount int     `json:"rejected_count"` // 已拒绝次数
}
