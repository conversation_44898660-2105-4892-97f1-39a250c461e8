package auth

import (
	"context"
	"serverless-aig/internal/client/supabase"
	"serverless-aig/internal/domain/auth"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/repository/interfaces"
	"serverless-aig/internal/repository/postgres"
	"serverless-aig/internal/service/wallet"
	"serverless-aig/pkg/errors"

	"github.com/google/uuid"
	"github.com/supabase-community/auth-go/types"
)

// Service 认证服务接口
type Service interface {
	Login(req *auth.LoginRequest) (*auth.AuthResponse, error)
	Register(req *auth.RegisterRequest) (*auth.AuthResponse, error)
	RefreshToken(req *auth.RefreshTokenRequest) (*auth.AuthResponse, error)
	Recover(req *auth.RecoverRequest) error
	Verify(req *auth.VerifyRequest) (*auth.AuthResponse, error)
	UpdatePassword(accessToken string, req *auth.UpdatePasswordRequest) error
	SendEmailCode(req *auth.EmailCodeRequest) error
	EmailLogin(req *auth.EmailLoginRequest) (*auth.AuthResponse, error)

	// 管理员功能
	AdminGetUserList(req *auth.UserListRequest) (*auth.UserListResponse, error)
	AdminCreateUser(req *auth.CreateUserRequest) (*auth.AuthResponse, error)
	AdminGetUserInfo(userID string) (*auth.User, error)
}

// service 认证服务实现
type service struct {
	supabaseClient *supabase.Client
	db             *postgres.DB
	aiRepo         interfaces.AIRepository
	walletService  wallet.Service
	logger         *logger.Logger
}

// NewService 创建认证服务
func NewService(supabaseClient *supabase.Client, db *postgres.DB, walletService wallet.Service, logger *logger.Logger) Service {
	aiRepo := postgres.NewAIRepository(db)
	return &service{
		supabaseClient: supabaseClient,
		db:             db,
		aiRepo:         aiRepo,
		walletService:  walletService,
		logger:         logger,
	}
}

// Login 用户登录
func (s *service) Login(req *auth.LoginRequest) (*auth.AuthResponse, error) {
	tokenResp, err := s.supabaseClient.Login(req.Email, req.Password)
	if err != nil {
		s.logger.LogError("用户登录失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "登录失败", err)
	}

	// 获取用户信息
	user, err := s.supabaseClient.GetUser(tokenResp.AccessToken)
	if err != nil {
		s.logger.LogError("获取用户信息失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "获取用户信息失败", err)
	}

	// 获取支持的科目列表
	subjects, err := s.getSubjects()
	if err != nil {
		s.logger.LogError("获取科目列表失败: %v", err)
		// 科目列表获取失败不影响登录，返回空列表
		subjects = []auth.Subject{}
	}

	return &auth.AuthResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresIn:    tokenResp.ExpiresIn,
		ExpiresAt:    tokenResp.ExpiresAt,
		User:         convertUserResponse(user),
		Subjects:     subjects,
	}, nil
}

// Register 用户注册
func (s *service) Register(req *auth.RegisterRequest) (*auth.AuthResponse, error) {
	user, err := s.supabaseClient.Register(req.Email, req.Password)
	if err != nil {
		s.logger.LogError("用户注册失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "注册失败", err)
	}

	// 创建钱包（新用户注册奖励）
	if err := s.createWalletForNewUser(user.ID); err != nil {
		s.logger.LogError("创建用户钱包失败: %v", err)
		// 注册成功但钱包创建失败，记录错误但不影响注册流程
	}

	return &auth.AuthResponse{
		User: convertUserResponse(user),
	}, nil
}

// RefreshToken 刷新令牌
func (s *service) RefreshToken(req *auth.RefreshTokenRequest) (*auth.AuthResponse, error) {
	tokenResp, err := s.supabaseClient.RefreshToken(req.RefreshToken)
	if err != nil {
		s.logger.LogError("刷新令牌失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "刷新令牌失败", err)
	}

	return &auth.AuthResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresIn:    tokenResp.ExpiresIn,
		ExpiresAt:    tokenResp.ExpiresAt,
	}, nil
}

// Recover 密码恢复
func (s *service) Recover(req *auth.RecoverRequest) error {
	err := s.supabaseClient.SendPasswordResetEmail(req.Email)
	if err != nil {
		s.logger.LogError("发送密码重置邮件失败: %v", err)
		return errors.NewExternalServiceError("supabase", "发送密码重置邮件失败", err)
	}

	return nil
}

// Verify 验证
func (s *service) Verify(req *auth.VerifyRequest) (*auth.AuthResponse, error) {
	tokenResp, err := s.supabaseClient.VerifyOTP(req.Email, req.Token, req.Type)
	if err != nil {
		s.logger.LogError("验证失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "验证失败", err)
	}

	return &auth.AuthResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresIn:    tokenResp.ExpiresIn,
		ExpiresAt:    tokenResp.ExpiresAt,
	}, nil
}

// UpdatePassword 更新密码
func (s *service) UpdatePassword(accessToken string, req *auth.UpdatePasswordRequest) error {
	_, err := s.supabaseClient.UpdatePassword(accessToken, req.Password)
	if err != nil {
		s.logger.LogError("更新密码失败: %v", err)
		return errors.NewExternalServiceError("supabase", "更新密码失败", err)
	}

	return nil
}

// SendEmailCode 发送邮箱验证码
func (s *service) SendEmailCode(req *auth.EmailCodeRequest) error {
	err := s.supabaseClient.SendEmailOTP(req.Email)
	if err != nil {
		s.logger.LogError("发送邮箱验证码失败: %v", err)
		return errors.NewExternalServiceError("supabase", "发送邮箱验证码失败", err)
	}

	return nil
}

// EmailLogin 邮箱验证码登录
func (s *service) EmailLogin(req *auth.EmailLoginRequest) (*auth.AuthResponse, error) {
	tokenResp, err := s.supabaseClient.SignInWithOTP(req.Email, req.Code)
	if err != nil {
		s.logger.LogError("邮箱验证码登录失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "邮箱验证码登录失败", err)
	}

	return &auth.AuthResponse{
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		ExpiresIn:    tokenResp.ExpiresIn,
		ExpiresAt:    tokenResp.ExpiresAt,
	}, nil
}

// AdminGetUserList 管理员获取用户列表
func (s *service) AdminGetUserList(req *auth.UserListRequest) (*auth.UserListResponse, error) {
	res, err := s.supabaseClient.AdminListUsers(req.Page, req.PerPage)
	if err != nil {
		s.logger.LogError("获取用户列表失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "获取用户列表失败", err)
	}
	count := len(res.Users)
	users := make([]auth.User, count)
	for _, user := range res.Users {
		users = append(users, convertUser(user))
	}

	return &auth.UserListResponse{
		Users:   users,
		Count:   count,
		Success: true,
	}, nil
}

// AdminCreateUser 管理员创建用户
func (s *service) AdminCreateUser(req *auth.CreateUserRequest) (*auth.AuthResponse, error) {
	user, err := s.supabaseClient.AdminCreateUser(req.Email, req.Password)
	if err != nil {
		s.logger.LogError("管理员创建用户失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "创建用户失败", err)
	}

	// 创建钱包
	if err := s.createWalletForNewUser(user.ID); err != nil {
		s.logger.LogError("创建用户钱包失败: %v", err)
	}

	return &auth.AuthResponse{
		User: convertUserResponse(user),
	}, nil
}

// AdminGetUserInfo 管理员获取用户信息
func (s *service) AdminGetUserInfo(userID string) (*auth.User, error) {
	user, err := s.supabaseClient.AdminGetUser(userID)
	if err != nil {
		s.logger.LogError("获取用户信息失败: %v", err)
		return nil, errors.NewExternalServiceError("supabase", "获取用户信息失败", err)
	}

	return convertUserResponse(user), nil
}

// createWalletForNewUser 为新用户创建钱包
func (s *service) createWalletForNewUser(userID uuid.UUID) error {
	ctx := context.Background()

	// 调用钱包服务创建钱包和注册奖励
	// 参考database_operations.go的EnsureWalletExists方法，钱包服务会：
	// 1. 创建新钱包，初始化付费余额为0，免费余额为500
	// 2. 创建注册奖励交易记录
	_, err := s.walletService.CreateWalletForNewUser(ctx, userID)
	if err != nil {
		s.logger.LogError("创建用户钱包失败: userID=%s, error=%v", userID, err)
		return err
	}

	s.logger.LogInfo("成功为新用户创建钱包: userID=%s", userID)
	return nil
}

// getSubjects 获取支持的科目列表
func (s *service) getSubjects() ([]auth.Subject, error) {
	ctx := context.Background()

	// 从数据库获取科目列表（只查询 prompt_key 和 description）
	subjectItems, err := s.aiRepo.GetSubjects(ctx)
	if err != nil {
		return nil, err
	}

	// 转换为科目列表
	subjects := make([]auth.Subject, 0, len(subjectItems))
	for _, item := range subjectItems {
		subjects = append(subjects, auth.Subject{
			Key:         item.Key,
			Description: item.Description,
		})
	}

	return subjects, nil
}

// convertUserResponse 转换用户类型
func convertUserResponse(user *types.UserResponse) *auth.User {
	return &auth.User{
		ID:        user.ID,
		Email:     user.Email,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}
}

func convertUser(user types.User) auth.User {
	return auth.User{
		ID:        user.ID,
		Email:     user.Email,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}
}
