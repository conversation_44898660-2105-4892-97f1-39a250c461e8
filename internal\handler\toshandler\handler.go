package toshandler

import (
	"fmt"

	"serverless-aig/internal/config"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/middleware"
	"serverless-aig/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/volcengine/volcengine-go-sdk/service/sts"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/credentials"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
)

// TOSCredentials 表示TOS临时访问凭证
type TOSCredentials struct {
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	SessionToken    string `json:"session_token"`
	Expiration      string `json:"expiration"`
}

// TOSCredentialsResponse 表示TOS临时访问凭证的响应
type TOSCredentialsResponse struct {
	Success          bool           `json:"success"`
	Credentials      TOSCredentials `json:"credentials"`
	Bucket           string         `json:"bucket"`
	Endpoint         string         `json:"endpoint"`
	IntranetEndpoint string         `json:"intranet_endpoint"`
	Region           string         `json:"region"`
	Message          string         `json:"message,omitempty"`
}

// Handler TOS处理器
type Handler struct {
	config *config.Config
	logger *logger.Logger
}

// New 创建TOS处理器
func New(config *config.Config, logger *logger.Logger) *Handler {
	return &Handler{
		config: config,
		logger: logger,
	}
}

// validateTOSConfig 验证TOS配置
func (h *Handler) validateTOSConfig() error {
	if h.config.TOS.AccessKey == "" {
		return fmt.Errorf("VOLC_ACCESS_KEY环境变量未设置")
	}
	if h.config.TOS.SecretKey == "" {
		return fmt.Errorf("VOLC_SECRET_KEY环境变量未设置")
	}
	if h.config.TOS.RoleArn == "" {
		return fmt.Errorf("VOLC_ROLE_ARN环境变量未设置")
	}
	return nil
}

// GetCredentials 获取TOS临时凭证
// @Summary 获取TOS临时凭证
// @Description 获取访问火山引擎对象存储的临时凭证，用于上传图片等文件，凭证有效期1小时
// @Tags 文件存储
// @Security BearerAuth
// @Produce json
// @Success 200 {object} TOSCredentialsResponse "临时凭证信息，包含访问密钥、会话令牌和存储桶信息"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 500 {object} models.SwaggerServiceUnavailableErrorResponse "服务器内部错误或STS服务不可用"
// @Router /api/v1/tos/credentials [get]
func (h *Handler) GetCredentials(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	if user == nil {
		response.Unauthorized(c, nil)
		return
	}

	// 验证配置
	if err := h.validateTOSConfig(); err != nil {
		h.logger.LogError("TOS配置无效: %v", err)
		response.InternalServerError(c, fmt.Errorf("STS配置无效: %s", err.Error()))
		return
	}

	// 使用SDK创建会话
	sess, err := session.NewSession(&volcengine.Config{
		Region:      volcengine.String(h.config.TOS.Region),
		Credentials: credentials.NewStaticCredentials(h.config.TOS.AccessKey, h.config.TOS.SecretKey, ""),
		Endpoint:    volcengine.String(h.config.TOS.STSBaseURL),
	})
	if err != nil {
		h.logger.LogError("创建火山引擎会话失败: %v", err)
		response.InternalServerError(c, fmt.Errorf("创建云会话失败: %s", err.Error()))
		return
	}

	// 创建STS服务客户端
	stsClient := sts.New(sess)

	// 准备AssumeRole请求
	roleSessionName := "serverless-aig-" + user.ID.String()
	assumeRoleInput := &sts.AssumeRoleInput{
		RoleTrn:         volcengine.String(h.config.TOS.RoleArn),
		RoleSessionName: volcengine.String(roleSessionName),
		DurationSeconds: volcengine.Int32(3600), // 1小时有效期
	}

	// 调用AssumeRole API
	assumeRoleOutput, err := stsClient.AssumeRole(assumeRoleInput)
	if err != nil {
		h.logger.LogError("调用火山引擎STS AssumeRole服务失败: %v", err)
		response.InternalServerError(c, fmt.Errorf("调用火山引擎STS AssumeRole服务失败: %s", err.Error()))
		return
	}

	// 构建TOS临时访问凭证响应
	credentialsResp := TOSCredentialsResponse{
		Success: true,
		Credentials: TOSCredentials{
			AccessKeyID:     *assumeRoleOutput.Credentials.AccessKeyId,
			SecretAccessKey: *assumeRoleOutput.Credentials.SecretAccessKey,
			SessionToken:    *assumeRoleOutput.Credentials.SessionToken,
			Expiration:      *assumeRoleOutput.Credentials.ExpiredTime,
		},
		Bucket:           h.config.TOS.Bucket,
		Endpoint:         h.config.TOS.Endpoint,
		IntranetEndpoint: h.config.TOS.IntranetEndpoint,
		Region:           h.config.TOS.Region,
	}

	h.logger.LogInfo("成功为用户 %s 生成TOS临时凭证", user.ID.String())
	response.Success(c, credentialsResp)
}
