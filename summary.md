# Summary

这是一个基于Go语言开发的Serverless后端服务项目，主要特点如下：

## 核心功能

- AI服务集成：支持火山引擎的图文理解、OCR分析等AI模型API
用户认证：基于Supabase的身份验证系统，支持邮箱注册/登录、验证码登录、密码重置
钱包系统：用户余额管理、充值申请、交易记录查询
管理员功能：充值审核、用户管理、统计查询、数据库迁移

## 技术架构

部署平台：腾讯云函数(SCF)
数据库：Turso(SQLite)，使用goose进行迁移管理
日志服务：OpenObserve
存储服务：腾讯云对象存储(TOS)
API文档：集成Swagger

## 服务模式

支持两种部署模式：

用户服务：包含公共路由、钱包功能、AI接口
管理员服务：包含管理功能、充值审核，不含AI接口
特色功能
多模态AI分析：支持4种分析模式(传统、深度思考、智能均衡、经济极速)
智能阅卷：专门针对高中物理等学科的自动阅卷功能
灵活充值：通过公众号+支付宝转账的充值方案，避免第三方支付手续费
项目采用依赖注入容器设计，具有良好的模块化和可维护性。
