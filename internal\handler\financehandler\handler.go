package financehandler

import (
	"strconv"

	"serverless-aig/internal/domain/finance"
	"serverless-aig/internal/logger"
	"serverless-aig/internal/middleware"
	"serverless-aig/pkg/response"

	financeservice "serverless-aig/internal/service/finance"

	"github.com/gin-gonic/gin"
)

// Handler 财务处理器
type Handler struct {
	financeService financeservice.Service
	logger         *logger.Logger
}

// New 创建财务处理器
func New(financeService financeservice.Service, logger *logger.Logger) *Handler {
	return &Handler{
		financeService: financeService,
		logger:         logger,
	}
}

// AddExpense 新增支出记录
// @Summary 新增支出记录
// @Description 管理员权限，用于录入一笔新的运营支出，包括服务器、AI模型调用等各类成本
// @Tags 管理员财务
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body finance.AddExpenseRequest true "支出详情" example({"description":"阿里云ECS服务器","amount":50000,"category":"infrastructure","vendor":"阿里云","transaction_date":"2024-01-01","service_period_start":"2024-01-01","service_period_end":"2024-12-31","payment_method":"alipay","invoice_url":"https://example.com/invoice.pdf"})
// @Success 200 {object} models.SwaggerEmptyResponse "支出记录创建成功"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如金额格式不正确或类别无效"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 403 {object} models.SwaggerForbiddenErrorResponse "权限不足，需要管理员权限"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/admin/finance/expense [post]
func (h *Handler) AddExpense(c *gin.Context) {
	user := middleware.GetUserFromContext(c)
	if user == nil {
		response.Unauthorized(c, nil)
		return
	}

	var req finance.AddExpenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	err := h.financeService.AddExpense(c.Request.Context(), user.ID, &req)
	if err != nil {
		h.logger.LogError("新增支出记录失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.SuccessWithMessage(c, nil, "支出记录创建成功")
}

// GetReport 获取财务报表
// @Summary 获取财务报表
// @Description 管理员权限，获取指定时间段的综合财务报表，包含收入、支出、利润和用户指标
// @Tags 管理员财务
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param body body finance.FinancialReportRequest true "报表请求" example({"start_date":"2024-01-01","end_date":"2024-01-31"})
// @Success 200 {object} finance.FinancialReport "财务报表，包含收入支出明细和用户指标"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如日期格式不正确"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 403 {object} models.SwaggerForbiddenErrorResponse "权限不足，需要管理员权限"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/admin/finance/report [post]
func (h *Handler) GetReport(c *gin.Context) {
	var req finance.FinancialReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, "request", err.Error())
		return
	}

	report, err := h.financeService.GetFinancialReport(c.Request.Context(), &req)
	if err != nil {
		h.logger.LogError("获取财务报表失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.Success(c, report)
}

// GetAnalysis 获取支出分析
// @Summary 获取支出分析
// @Description 管理员权限，按类别分析指定时间段的支出分布和趋势
// @Tags 管理员财务
// @Security BearerAuth
// @Produce json
// @Param start_date query string true "开始日期，格式：YYYY-MM-DD" format(date) example("2024-01-01")
// @Param end_date query string true "结束日期，格式：YYYY-MM-DD" format(date) example("2024-01-31")
// @Success 200 {object} finance.ExpenseAnalysis "支出分析结果，包含各类别支出统计"
// @Failure 400 {object} models.SwaggerValidationErrorResponse "请求参数错误，如日期格式不正确或日期范围无效"
// @Failure 401 {object} models.SwaggerUnauthorizedErrorResponse "未授权，需要有效的访问令牌"
// @Failure 403 {object} models.SwaggerForbiddenErrorResponse "权限不足，需要管理员权限"
// @Failure 500 {object} models.SwaggerInternalServerErrorResponse "服务器内部错误"
// @Router /api/v1/admin/finance/analysis [get]
func (h *Handler) GetAnalysis(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" {
		response.ValidationError(c, "start_date", "开始日期不能为空")
		return
	}

	if endDate == "" {
		response.ValidationError(c, "end_date", "结束日期不能为空")
		return
	}

	analysis, err := h.financeService.GetExpenseAnalysis(c.Request.Context(), startDate, endDate)
	if err != nil {
		h.logger.LogError("获取支出分析失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.Success(c, analysis)
}

// ListExpenses 获取支出列表
// @Summary 获取支出列表
// @Description 管理员权限，获取支出记录列表
// @Tags 管理员财务
// @Security BearerAuth
// @Produce json
// @Param category query string false "支出类别"
// @Param start_date query string false "开始日期" format(date)
// @Param end_date query string false "结束日期" format(date)
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} finance.ExpenseListResponse "支出列表"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Router /api/v1/admin/finance/expenses [get]
func (h *Handler) ListExpenses(c *gin.Context) {
	req := &finance.ExpenseListRequest{
		Category:  c.Query("category"),
		StartDate: c.Query("start_date"),
		EndDate:   c.Query("end_date"),
		Limit:     20,
		Offset:    0,
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			req.Limit = l
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			req.Offset = o
		}
	}

	resp, err := h.financeService.GetExpenses(c.Request.Context(), req)
	if err != nil {
		h.logger.LogError("获取支出列表失败: %v", err)
		response.BadRequest(c, err)
		return
	}

	response.Success(c, resp)
}
