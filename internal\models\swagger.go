package models

// SwaggerResponse 统一的Swagger响应模型
// @Description 标准API响应格式
type SwaggerResponse struct {
	Success bool   `json:"success" example:"true"`                    // 请求是否成功
	Data    any    `json:"data,omitempty"`                            // 响应数据
	Message string `json:"message,omitempty" example:"操作成功"`          // 成功消息
	Error   string `json:"error,omitempty" example:"参数验证失败"`          // 错误信息
	Code    string `json:"code,omitempty" example:"VALIDATION_ERROR"` // 错误代码
}

// SwaggerSuccessResponse 成功响应模型
// @Description 成功响应格式
type SwaggerSuccessResponse struct {
	Success bool   `json:"success" example:"true"`           // 请求成功标识
	Data    any    `json:"data,omitempty"`                   // 响应数据
	Message string `json:"message,omitempty" example:"操作成功"` // 成功消息
}

// SwaggerErrorResponse 错误响应模型
// @Description 错误响应格式
type SwaggerErrorResponse struct {
	Success bool   `json:"success" example:"false"`                   // 请求失败标识
	Error   string `json:"error" example:"参数验证失败"`                    // 错误信息
	Code    string `json:"code,omitempty" example:"VALIDATION_ERROR"` // 错误代码
	Message string `json:"message,omitempty" example:"请求参数不正确"`       // 错误描述
}

// SwaggerValidationErrorResponse 验证错误响应模型
// @Description 参数验证错误响应格式
type SwaggerValidationErrorResponse struct {
	Success bool   `json:"success" example:"false"`         // 请求失败标识
	Error   string `json:"error" example:"email字段格式不正确"`    // 具体验证错误信息
	Code    string `json:"code" example:"VALIDATION_ERROR"` // 错误代码
	Message string `json:"message" example:"请求参数验证失败"`      // 错误描述
}

// SwaggerUnauthorizedErrorResponse 未授权错误响应模型
// @Description 未授权错误响应格式
type SwaggerUnauthorizedErrorResponse struct {
	Success bool   `json:"success" example:"false"`     // 请求失败标识
	Error   string `json:"error" example:"访问令牌无效或已过期"`  // 错误信息
	Code    string `json:"code" example:"UNAUTHORIZED"` // 错误代码
	Message string `json:"message" example:"未授权访问"`     // 错误描述
}

// SwaggerForbiddenErrorResponse 权限不足错误响应模型
// @Description 权限不足错误响应格式
type SwaggerForbiddenErrorResponse struct {
	Success bool   `json:"success" example:"false"`  // 请求失败标识
	Error   string `json:"error" example:"需要管理员权限"`  // 错误信息
	Code    string `json:"code" example:"FORBIDDEN"` // 错误代码
	Message string `json:"message" example:"权限不足"`   // 错误描述
}

// SwaggerNotFoundErrorResponse 资源不存在错误响应模型
// @Description 资源不存在错误响应格式
type SwaggerNotFoundErrorResponse struct {
	Success bool   `json:"success" example:"false"`  // 请求失败标识
	Error   string `json:"error" example:"用户不存在"`    // 错误信息
	Code    string `json:"code" example:"NOT_FOUND"` // 错误代码
	Message string `json:"message" example:"资源不存在"`  // 错误描述
}

// SwaggerInternalServerErrorResponse 服务器内部错误响应模型
// @Description 服务器内部错误响应格式
type SwaggerInternalServerErrorResponse struct {
	Success bool   `json:"success" example:"false"`       // 请求失败标识
	Error   string `json:"error" example:"数据库连接失败"`       // 错误信息
	Code    string `json:"code" example:"INTERNAL_ERROR"` // 错误代码
	Message string `json:"message" example:"服务器内部错误"`     // 错误描述
}

// SwaggerServiceUnavailableErrorResponse 服务不可用错误响应模型
// @Description 服务不可用错误响应格式
type SwaggerServiceUnavailableErrorResponse struct {
	Success bool   `json:"success" example:"false"`            // 请求失败标识
	Error   string `json:"error" example:"AI服务暂时不可用"`          // 错误信息
	Code    string `json:"code" example:"SERVICE_UNAVAILABLE"` // 错误代码
	Message string `json:"message" example:"外部服务不可用"`          // 错误描述
}

// SwaggerInsufficientBalanceErrorResponse 余额不足错误响应模型
// @Description 余额不足错误响应格式
type SwaggerInsufficientBalanceErrorResponse struct {
	Success bool   `json:"success" example:"false"`             // 请求失败标识
	Error   string `json:"error" example:"当前余额不足以完成此操作"`        // 错误信息
	Code    string `json:"code" example:"INSUFFICIENT_BALANCE"` // 错误代码
	Message string `json:"message" example:"余额不足"`              // 错误描述
}

// SwaggerPaginationResponse 分页响应模型
// @Description 分页数据响应格式
type SwaggerPaginationResponse struct {
	Success bool `json:"success" example:"true"` // 请求成功标识
	Data    struct {
		Items  []any `json:"items"`                        // 数据列表
		Count  int   `json:"count" example:"20"`           // 当前页数量
		Total  int   `json:"total" example:"100"`          // 总数量
		Limit  int   `json:"limit,omitempty" example:"20"` // 每页限制
		Offset int   `json:"offset,omitempty" example:"0"` // 偏移量
	} `json:"data"`
	Message string `json:"message,omitempty" example:"获取数据成功"` // 成功消息
}

// SwaggerEmptyResponse 空响应模型
// @Description 无数据响应格式，通常用于删除或更新操作
type SwaggerEmptyResponse struct {
	Success bool   `json:"success" example:"true"` // 请求成功标识
	Message string `json:"message" example:"操作成功"` // 成功消息
}
